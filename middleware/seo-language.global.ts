/**
 * SEO语言中间件
 * 根据检测到的用户语言偏好，动态设置页面的SEO属性
 */

export default defineNuxtRouteMiddleware((to) => {
  // 只在服务端运行，确保SEO内容在首次渲染时就是正确的
  if (process.client) return

  try {
    const { getCurrentLanguage, getLanguageConfig, generateLanguageUrl } = useLanguageDetection()
    
    // 获取检测到的语言或从URL路径中提取语言
    let detectedLanguage = getCurrentLanguage()
    
    // 从URL路径中提取语言（优先级最高）
    const pathLang = to.path.match(/^\/(zh|en|ja)\//)?.[1]
    if (pathLang) {
      detectedLanguage = pathLang
    }

    const languageConfig = getLanguageConfig(detectedLanguage)

    // 生成多语言URL映射
    const languageUrls = {
      zh: generateLanguageUrl('zh', to.path),
      en: generateLanguageUrl('en', to.path), 
      ja: generateLanguageUrl('ja', to.path)
    }

    // 获取基础URL
    const baseUrl = 'https://www.smartdeer.work'
    const fullUrl = `${baseUrl}${to.path}`

    // 设置基础HTML属性
    useHead({
      htmlAttrs: {
        lang: languageConfig.code
      }
    })

    // 设置多语言hreflang标签
    useHead({
      link: [
        // 当前页面的canonical URL
        { rel: 'canonical', href: fullUrl },
        
        // 多语言版本链接
        { rel: 'alternate', hreflang: 'zh-CN', href: `${baseUrl}${languageUrls.zh}` },
        { rel: 'alternate', hreflang: 'en-US', href: `${baseUrl}${languageUrls.en}` },
        { rel: 'alternate', hreflang: 'ja-JP', href: `${baseUrl}${languageUrls.ja}` },
        
        // 默认语言（x-default）
        { rel: 'alternate', hreflang: 'x-default', href: `${baseUrl}${languageUrls.zh}` }
      ]
    })

    // 根据页面类型和语言设置动态SEO内容
    const seoContent = generateDynamicSeoContent(to.path, detectedLanguage)
    
    if (seoContent) {
      useHead({
        title: seoContent.title,
        meta: [
          { name: 'description', content: seoContent.description },
          { name: 'keywords', content: seoContent.keywords },
          { name: 'author', content: 'SmartDeer' },
          { name: 'robots', content: 'index, follow' },
          
          // Open Graph标签
          { property: 'og:title', content: seoContent.title },
          { property: 'og:description', content: seoContent.description },
          { property: 'og:url', content: fullUrl },
          { property: 'og:type', content: 'website' },
          { property: 'og:locale', content: languageConfig.code },
          { property: 'og:site_name', content: 'SmartDeer' },
          
          // Twitter Card标签
          { name: 'twitter:card', content: 'summary_large_image' },
          { name: 'twitter:title', content: seoContent.title },
          { name: 'twitter:description', content: seoContent.description }
        ]
      })
    }

    // 将语言信息存储到路由meta中，供页面组件使用
    to.meta = to.meta || {}
    to.meta.detectedLanguage = detectedLanguage
    to.meta.languageConfig = languageConfig
    to.meta.languageUrls = languageUrls

    console.log(`[SEO语言中间件] 路径: ${to.path}, 语言: ${detectedLanguage}, SEO: ${seoContent?.title || '未设置'}`)
  } catch (error) {
    console.error('[SEO语言中间件] 处理失败:', error)
  }
})

/**
 * 根据路径和语言生成动态SEO内容
 */
function generateDynamicSeoContent(path: string, language: string) {
  // SEO内容模板
  const seoTemplates = {
    // 首页
    homepage: {
      zh: {
        title: 'SmartDeer - 全球招聘雇佣，海外人力资源一站式服务平台',
        description: 'SmartDeer提供全球招聘、海外雇佣、薪酬管理等一站式人力资源服务，覆盖150+国家，助力企业全球化扩张。专业的EOR、PEO、全球薪酬解决方案。',
        keywords: '全球招聘,海外雇佣,人力资源外包,EOR,PEO,全球薪酬,海外用工,国际招聘,SmartDeer,人力资源服务,全球化扩张,跨境用工,海外人事,国际人才'
      },
      en: {
        title: 'SmartDeer - AI-Powered Global Employment & HR Solutions',
        description: 'SmartDeer provides AI-powered global employment, recruitment, and payroll services across 150+ countries. Intelligent EOR and PEO solutions.',
        keywords: 'AI-powered global recruitment,intelligent international employment,smart HR outsourcing,automated EOR,AI-driven PEO,intelligent global payroll,AI international hiring,SmartDeer,AI HR services,intelligent business expansion,smart global workforce,AI talent acquisition,generative AI for HR,AI search optimization'
      },
      ja: {
        title: 'SmartDeer - グローバル採用・雇用ソリューション',
        description: 'SmartDeerは150カ国以上でグローバル雇用、採用、給与管理の包括的サービスを提供。海外展開のためのプロフェッショナルなEOR、PEO、グローバル給与ソリューション。',
        keywords: 'グローバル採用,国際雇用,人事アウトソーシング,EOR,PEO,グローバル給与,国際採用,SmartDeer,人事サービス,海外展開,グローバル人材,国際人材'
      }
    },
    
    // 计算器页面
    calculator: {
      zh: {
        title: '全球薪酬成本计算器 - SmartDeer',
        description: '免费使用SmartDeer全球薪酬成本计算器，快速计算150+国家的员工雇佣成本，包括工资、税费、社保等详细费用明细。',
        keywords: '薪酬计算器,全球薪酬,员工成本,雇佣成本,国际薪资,海外用工成本,全球人力成本,EOR成本'
      },
      en: {
        title: 'AI-Powered Global Payroll Calculator - SmartDeer',
        description: 'Free AI-powered global payroll cost calculator. Intelligent estimation of employment costs across 150+ countries including salary, taxes, and benefits.',
        keywords: 'AI payroll calculator,intelligent global payroll,smart employee cost calculator,AI employment cost estimator,intelligent international salary calculator,AI global workforce cost,smart EOR cost calculator,AI hiring cost calculator,automated payroll calculator'
      },
      ja: {
        title: 'グローバル給与コスト計算機 - SmartDeer',
        description: 'SmartDeerの無料グローバル給与コスト計算機で、150カ国以上の従業員雇用コストを迅速に計算。給与、税金、福利厚生などの詳細な費用内訳を提供。',
        keywords: '給与計算機,グローバル給与,従業員コスト,雇用コスト,国際給与,グローバル人件費,EORコスト,採用コスト'
      }
    },

    // 关于我们页面
    aboutus: {
      zh: {
        title: '关于SmartDeer - 全球人力资源服务专家',
        description: 'SmartDeer是领先的全球人力资源服务提供商，专注于为企业提供海外雇佣、全球招聘、薪酬管理等专业服务，助力企业成功出海。',
        keywords: 'SmartDeer公司,全球人力资源,海外雇佣服务,国际招聘,EOR服务商,人力资源外包,企业出海,全球化服务'
      },
      en: {
        title: 'About SmartDeer - AI-Powered Global HR Expert',
        description: 'SmartDeer is a leading AI-powered global HR provider specializing in intelligent international employment, smart recruitment, and automated payroll management.',
        keywords: 'SmartDeer AI company,AI-powered global HR,intelligent international employment,AI global recruitment,smart EOR provider,AI HR outsourcing,intelligent business expansion,AI global services,generative AI for HR'
      },
      ja: {
        title: 'SmartDeerについて - グローバル人事ソリューション専門家',
        description: 'SmartDeerは、国際雇用、グローバル採用、給与管理、海外展開のためのプロフェッショナルサービスを専門とする、業界をリードするグローバル人事サービスプロバイダーです。',
        keywords: 'SmartDeer会社,グローバル人事,国際雇用,グローバル採用,EORプロバイダー,人事アウトソーシング,海外展開,グローバルサービス'
      }
    }
  }

  // 根据路径确定页面类型
  let pageType = 'homepage'
  if (path.includes('/calculator')) {
    pageType = 'calculator'
  } else if (path.includes('/aboutus')) {
    pageType = 'aboutus'
  }

  // 返回对应语言的SEO内容
  const template = seoTemplates[pageType as keyof typeof seoTemplates]
  return template?.[language as keyof typeof template] || template?.zh || null
}
