<template lang="pug">
.contries-page
  site-header(lang="${LANGUAGE_SIMPLE}" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage" :alt="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage" :alt="flagImage")
    .countries-content
      h1.article-title ${TITLE}
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="${LANGUAGE_SIMPLE}" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="${LANGUAGE_SIMPLE}" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '${TITLE}',
  ogDescription: '${DESCRIPTION}',
  ogSiteName: '${SITE_NAME}',
  description: '${DESCRIPTION}'
})
useHead({
  htmlAttrs: {
    lang: '${LANGUAGE}'
  },
  title: '${TITLE}'
})
const status = reactive({
  showForm: false
})
const langSimple = '${LANGUAGE_SIMPLE}';
const pageTitle = '${TITLE}';
const bannerImage = '${FEATURE_IMAGE_URL}';
const flagImage = '${COUNTRY_FLAT_IMAGE_URL}';
const htmlContent = '${CONTENT}';
function submitSuccess() {
  ElMessage.success('${FORM_CONFIRM_PROMPT}')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>