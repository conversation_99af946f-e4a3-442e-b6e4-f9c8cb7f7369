<template lang="pug">
mixin header-nav
  section.header-nav
    NuxtLink(to="/")
      figure.logo
        img(src="~/assets/images/aboutus/sd_logo.png" alt="~/assets/images/aboutus/sd_logo.png")
    //- .page-title
    //-   h1 关于我们
    .extra
      .contact-us(@click="()=>{status.showForm = true}")
        .text 联系我们

      //- 吃瓜就要付出代价，否则影响页面跳转
      //- https://github.com/element-plus/element-plus/pull/9731
      client-only
        el-dropdown.language-selector
          .text(style="color: #000;") 中 / EN / 日
          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(@click="switchLang('zh')") 中文
              el-dropdown-item(@click="switchLang('en')") English
              el-dropdown-item(@click="switchLang('ja')") 日本語

mixin company-profile 
  section.company-profile
    .section-title
      h2.title 您的全球HR解决方案合作伙伴
      p About Us

    .profile-content
      .figure
        img(src="~/assets/images/aboutus/background.jpeg" alt="~/assets/images/aboutus/background.jpeg")
        h3 公司背景
        p SmartDeer 是一站式 “HR服务和SaaS” 平台，致力于全球招聘和雇佣解决方案。SmartDeer由挚信资本孵化，微光基金、WeWork 和 Hash Global 领投。SmartDeer 帮助企业打破地域限制，快速招聘全球人才，并管理包括入职、离职、薪酬、税务和福利在内的整个雇佣生命周期。我们的平台确保每个环节的合规性和高效性，简化全球团队的管理。
        p SmartDeer也已获得国际权威的信息安全管理体系 ISO 27001 认证，全面保障客户的数据安全与隐私，为客户提供值得信赖的服务体验。
      .figure.right
        img(src="~/assets/images/aboutus/globalization.jpeg" alt="~/assets/images/aboutus/globalization.jpeg")
        h3 全球影响力
        p SmartDeer 在香港和新加坡设有双总部，为我们提供了全球战略优势，同时确保符合当地的数据存储和处理法规。除了在美国、英国、阿联酋、沙特阿拉伯、澳大利亚、日本、韩国、泰国、马来西亚、印度尼西亚、菲律宾、越南、中国大陆、墨西哥和巴西等地区设有全资子公司和分支机构外，我们的服务还通过自营网络和合作伙伴覆盖全球150多个国家和地区。
      .figure
        img(src="~/assets/images/index/eor.webp" alt="~/assets/images/index/eor.webp")
        h3 我们的团队
        p SmartDeer 在全球十多个国家拥有超过 150 名员工，提供多语言服务，重点支持中英双语，并具备强大的各国本地语言服务能力，以满足区域需求。我们团队对当地法律法规有深入的理解，能够为客户提供专业的本地化服务，同时提供全球支持。
      .figure.right
        img(src="~/assets/images/aboutus/scope.jpeg" alt="~/assets/images/aboutus/scope.jpeg")
        h3 服务范围
        p 我们提供全面的 HR 解决方案，涵盖人才招聘、全球合规雇佣、签证办理、薪酬管理、福利和税务管理。SmartDeer 强大的全球 HR SaaS 系统帮助企业轻松应对全球HR运营的复杂性，简化流程并降低合规风险。我们的平台使企业能够在全球范围内招聘人才，管理入职和离职、假期、薪酬和税务，帮助企业在全球范围内取得成功。

mixin profession
  section.profession
    .profession-list
      .profession-item
        figure 
          img(src="~/assets/images/aboutus/earth.svg" alt="~/assets/images/aboutus/earth.svg")
        .content
          .title 全球协同办公
          .desc 在美国、英国、阿联酋、沙特阿拉伯、澳大利亚、新加坡、日本、韩国、泰国、马来西亚、印度尼西亚、菲律宾、越南、香港等地均有交付团队。

      .profession-item
        figure 
          img(src="~/assets/images/aboutus/team.svg" alt="~/assets/images/aboutus/team.svg")
        .content
          .title 10+年经验专业团队
          .desc 来自全球各地，拥有超过十年头部公司HR工作经验及人力资源咨询服务经验。

      .profession-item
        figure 
          img(src="~/assets/images/aboutus/cover.svg" alt="~/assets/images/aboutus/cover.svg")
        .content
          .title 业务范围全覆盖
          .desc 业务范围包括全球人才招聘、全球全职员工雇佣、全球灵活用工雇佣、全球人力服务专业外包。

mixin global-office
  section.global-office
    .section-title
      h2.title 全球办公室
      p Global Office

    .office
      .office-list
        .office-item
          figure 
            img(src="~/assets/images/aboutus/hongkong.webp" alt="~/assets/images/aboutus/hongkong.webp")
          .content
            .title 中国香港
            .location 地址：香港中环德辅道中141号中保集团大厦7楼705-706室
        .office-item
          figure
            img(src="~/assets/images/aboutus/singapore.webp" alt="~/assets/images/aboutus/singapore.webp")
          .content
            .title 新加坡
            .location 地址：3 Fraser Street, #5-25 Duo Tower, Singapore (189352)
        .office-item
          figure
            img(src="~/assets/images/aboutus/california.jpeg" alt="~/assets/images/aboutus/california.jpeg")
          .content
            .title 美国
            .location 地址：15025 PROCTOR AVAVE CITY OF INDUSTRY, Y, CA CA 91746
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/uk.jpeg" alt="~/assets/images/aboutus/uk.jpeg")
          .content
            .title 英国
            .location 地址：69 Aberdeen Avenue, Cambridge, England, CB2 8DL
        .office-item
          figure
            img(src="~/assets/images/aboutus/australia.jpeg" alt="~/assets/images/aboutus/australia.jpeg")
          .content
            .title 澳大利亚
            .location 地址：135 KING STREET, SYDNEY, NSW 2000
        .office-item
          figure
            img(src="~/assets/images/aboutus/aue.jpeg" alt="~/assets/images/aboutus/aue.jpeg")
          .content
            .title 阿联酋
            .location 地址：Office328,BlockB,Business Village, Deira, Dubai, UAE
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/japan.jpeg" alt="~/assets/images/aboutus/japan.jpeg")
          .content
            .title 日本
            .location 地址：神奈川県横浜市中区山下町98GSハイム山下町4階403号室
        .office-item
          figure
            img(src="~/assets/images/aboutus/korea.jpeg" alt="~/assets/images/aboutus/korea.jpeg")
          .content
            .title 韩国
            .location 地址：서울특별시 중랑구 동일로825，2층 205호 (중화동)
        .office-item
          figure
            img(src="~/assets/images/aboutus/thailand.jpeg" alt="~/assets/images/aboutus/thailand.jpeg")
          .content
            .title 泰国
            .location 地址：11 / 152-153 Room No. GDC 101, 1st Floor, Moo 5, Kookong District, Lam Luka District, Pathum Thani Province
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/malaysia.jpeg" alt="~/assets/images/aboutus/malaysia.jpeg")
          .content
            .title 马来西亚
            .location 地址：332F-­2, HARMONY SQUARE, JALAN PERAK 11600 JELUTONG PULAU PINANG MALAYSIA
        .office-item
          figure
            img(src="~/assets/images/aboutus/indonesia.jpeg" alt="~/assets/images/aboutus/indonesia.jpeg")
          .content
            .title 印度尼西亚
            .location 地址：Gedung Wirausaha Lantai 1 Unit 104, Jalan HR Rasuna Said Kav. C-5,Desa/Kelurahan Karet, Kec. Setiabudi, Kota Adm. Jakarta Selatan, ProvinsiDKI Jakarta
        .office-item
          figure
            img(src="~/assets/images/aboutus/philippines.jpeg" alt="~/assets/images/aboutus/philippines.jpeg")
          .content
            .title 菲律宾
            .location 地址：UNIT 25D 2ND FLOOR ZETA II BLDG.191 SALCEDO ST., SAN LORENZO,CITY OF MAKATI,FOURTH DIRECT, NATIONAL CAPITAL REGION (NCR), 1223
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/vietnam.jpeg" alt="~/assets/images/aboutus/vietnam.jpeg")
          .content
            .title 越南
            .location 地址：Room(s):28 ,Level 14, Saigon Centre Tower 1, No. 65 Le Loi Street, Ben Nghe Ward, District 1, Ho Chi Minh City,Vietnam
        .office-item
          figure
            img(src="~/assets/images/aboutus/beijing.webp" alt="~/assets/images/aboutus/beijing.webp")
          .content
            .title 北京
            .location 地址：北京市东城区王府井 王府国际中心 wework 4楼
        .office-item
          figure
            img(src="~/assets/images/aboutus/shanghai.webp" alt="~/assets/images/aboutus/shanghai.webp")
          .content
            .title 上海
            .location 地址：上海市普陀区曹杨路535号汇融大厦12层
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/shenzhen.webp" alt="~/assets/images/aboutus/shenzhen.webp")
          .content
            .title 深圳
            .location 地址：深圳市前海深港合作区南山街道临海大道59号海运中心口岸楼0701-D021
        .office-item
          figure
            img(src="~/assets/images/aboutus/hangzhou.webp" alt="~/assets/images/aboutus/hangzhou.webp")
          .content
            .title 杭州
            .location 地址：浙江省杭州市余杭区前街道龙泉路8-1号 2号楼205室

        .office-item
          figure
            img(src="~/assets/images/aboutus/chengdu.webp" alt="~/assets/images/aboutus/chengdu.webp")
          .content
            .title 成都
            .location 地址：四川省成都市成华区航天路5号利星行广场6F-K0063

mixin contact-us
  .contact-form
    //- 吃瓜就要付出代价，否则影响页面跳转
    //- https://github.com/element-plus/element-plus/pull/9731
    client-only
      el-dialog(v-model="status.showForm" title="" :width="600")
        contact-us-form(@submit="submitSuccess" lang="zh")

.page-about-us
  .header
    +header-nav
    +company-profile

  +profession
  +global-office

  +contact-us

  SiteFooter(lang="zh" @contact-us="()=>{ status.showForm = true }")

</template>

<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
definePageMeta({ layout: 'basic' })
useHead({
  htmlAttrs: { lang: 'zh-CN' },
  title: '关于SmartDeer - 全球人力资源解决方案提供商 | 出海企业首选合作伙伴',
  meta: [
    // Basic SEO
    { name: 'description', content: '了解SmartDeer，领先的全球人力资源解决方案提供商。我们提供专业EOR服务、国际招聘、薪酬管理，覆盖150+国家，在全球设有办事处，拥有ISO 27001认证，助力中国企业安全合规出海。' },
    { name: 'keywords', content: '关于SmartDeer, 全球人力资源公司, 国际雇佣服务, EOR服务商, 全球招聘公司, 人力资源解决方案, 国际薪酬, 出海企业服务, 全球合规, ISO认证' },
    { name: 'author', content: 'SmartDeer' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },

    // Canonical and hreflang
    { name: 'canonical', content: 'https://smartdeer.work/zh/aboutus' },

    // Open Graph
    { property: 'og:title', content: '关于SmartDeer - 全球人力资源解决方案提供商 | 出海企业首选' },
    { property: 'og:description', content: '了解SmartDeer，领先的全球人力资源解决方案提供商。我们提供EOR服务、国际招聘、薪酬管理，覆盖150+国家，在全球设有办事处。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'https://smartdeer.work/zh/aboutus' },
    { property: 'og:image', content: 'https://smartdeer.work/images/aboutus/company-overview-zh.png' },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:locale', content: 'zh_CN' },

    // Twitter Card
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: '关于SmartDeer - 全球人力资源解决方案提供商' },
    { name: 'twitter:description', content: '了解SmartDeer，领先的全球人力资源解决方案提供商，提供EOR服务、国际招聘、薪酬管理，覆盖150+国家。' },
    { name: 'twitter:image', content: 'https://smartdeer.work/images/aboutus/company-overview-zh.png' },
    { name: 'twitter:image:alt', content: 'SmartDeer全球办公室和团队' }
  ],
  link: [
    // Canonical URL
    { rel: 'canonical', href: 'https://smartdeer.work/zh/aboutus' },

    // Hreflang for multilingual SEO
    { rel: 'alternate', hreflang: 'en', href: 'https://smartdeer.work/en/aboutus' },
    { rel: 'alternate', hreflang: 'zh-CN', href: 'https://smartdeer.work/zh/aboutus' },
    { rel: 'alternate', hreflang: 'ja-JP', href: 'https://smartdeer.work/ja/aboutus' },
    { rel: 'alternate', hreflang: 'x-default', href: 'https://smartdeer.work/en/aboutus' }
  ],
  script: [
    // Structured Data - AboutPage
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "AboutPage",
        "name": "关于SmartDeer",
        "description": "了解SmartDeer，全球人力资源解决方案提供商，为企业提供全面的国际雇佣服务",
        "url": "https://smartdeer.work/zh/aboutus",
        "inLanguage": "zh-CN",
        "mainEntity": {
          "@type": "Organization",
          "name": "SmartDeer",
          "alternateName": "智鹿",
          "description": "全球人力资源解决方案和雇佣服务提供商",
          "url": "https://smartdeer.work",
          "logo": "https://smartdeer.work/images/logo.png",
          "foundingDate": "2020",
          "numberOfEmployees": "150+",
          "address": [
            {
              "@type": "PostalAddress",
              "addressLocality": "香港",
              "addressRegion": "香港",
              "streetAddress": "香港中环德辅道中141号中保集团大厦7楼705-706室",
              "addressCountry": "HK"
            },
            {
              "@type": "PostalAddress",
              "addressLocality": "新加坡",
              "addressRegion": "新加坡",
              "streetAddress": "3 Fraser Street, #5-25 Duo Tower",
              "postalCode": "189352",
              "addressCountry": "SG"
            }
          ],
          "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "availableLanguage": ["Chinese", "English", "Japanese"]
          },
          "sameAs": [
            "https://www.linkedin.com/company/smartdeer-global/"
          ],
          "areaServed": "全球",
          "serviceArea": {
            "@type": "GeoCircle",
            "name": "全球覆盖 - 150+国家"
          }
        }
      })
    }
  ]
})

const status = reactive({
  showForm: false
})

function switchLang(lang) {
  langTool.swithLang(lang, '/aboutus')
}

function submitSuccess() {
  status.showForm = false
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
</script>

<style lang="scss" scoped>
.page-about-us {
  section {
    .section-title {
      text-align: center;

      h2.title {
        font-size: 48px;
        position: relative;
        display: inline-block;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        line-height: 67px;

        &::before {
          content: '';
          display: block;
          position: absolute;
          left: 0;
          bottom: 11px;
          width: 100%;
          background-color: #FF8600;
          height: 8px;
          z-index: -1;
        }
      }

      p {
        font-size: 18px;
        font-family: DIN-Regular, DIN;
        color: #999999;
        line-height: 22px;
        letter-spacing: 5px;
        margin-top: 8px;
        margin-bottom: 0;
      }
    }
  }

  .header {
    background-image: url("~/assets/images/aboutus/map-bg.png");
    background-position: center 16px;
    background-repeat: no-repeat;
  }
}

section.header-nav {
  width: 1204px;
  margin: 0 auto;
  padding-top: 47px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;

  figure.logo {
    flex: 0 0 auto;

    img {
      width: 160px;
    }
  }

  .page-title {
    flex: 1 1 auto;
    padding-left: 24px;

    h1 {
      font-size: 20px;
      font-weight: bold;

      &::before {
        content: "|";
        display: inline-block;
        color: #BFBFBF;
        margin-right: 24px;
      }

    }
  }

  .extra {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #000000;

    .language-selector {
      margin-left: 50px;

      .text {
        font-size: 16px;
      }
    }
  }
}

section.company-profile {
  width: 1204px;
  margin: 0 auto;
  padding-top: 100px;
  box-sizing: border-box;

  .profile-content {
    align-items: center;
    margin-top: 70px;
    padding: 0 12px;
    margin-bottom: 40px;

    .figure {
      width: 1204px;
      height: 350px;
      margin-bottom: 20px;
      img {
        width: 580px;
        height: 310px;
        float: left;
        margin: 0 40px 25px 0;
        border-radius: 15px;
      }
      &::after {
        content: '';
        clear: both;
      }
    }

    .figure.right {
      img {
        float: right;
        margin: 0 0 25px 40px;
      }
      &::after {
        content: '';
        clear: both;
      }
    }

    h3 {
      font-size: 24px;
      color: #333;
      margin-bottom: 10px;
    }
    h4 {
      color: #333;
      font-size: 20px;
      margin-top: 25px;
      margin-bottom: 10px;
    }
    p {
      color: #333;
      font-size: 18px;
      line-height: 26px;
      letter-spacing: 1px;
      margin: 0 0 25px;
    }
  }
}

section.profession {
  background-color: #F7F9FA;

  .profession-list {
    width: 1204px;
    margin: 0 auto;
    display: flex;
    padding-top: 40px;
    justify-content: space-between;
    height: 326px;


    .profession-item {
      width: 360px;

      figure {
        img {
          width: 64px;
          margin: 0 auto;
          margin-bottom: 24px;
        }
      }

      .content {
        margin-top: 32px;

        .title {
          font-size: 26px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333333;
          line-height: 37px;
          text-align: center;
        }

        .desc {
          font-size: 18px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          line-height: 29px;
          margin-top: 24px;
        }
      }
    }
  }
}

section.global-office {
  width: 1204px;
  margin: 0 auto;
  padding: 156px 0 200px 0;

  .office-list {
    margin-top: 60px;
    display: flex;
    justify-content: space-between;

    &:first-child{
      margin-top: 70px;
    }

    .office-item {
      width: 382px;
      box-sizing: border-box;

      figure {
        img {
          width: 100%;
          height: 246px;
          display: block;
          border-radius: 20px;
        }
      }

      .content {
        margin-top: 40px;
        .title {
          font-size: 26px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333333;
          line-height: 32px;
          text-align: center;
          margin-bottom: 18px;
        }

        .location, .contact{
          font-size: 18px;
          font-family: PingFangSC-Medium, PingFang SC;
          color: #333333;
          line-height: 29px;
          width: 348px;
        }
      }
    }
  }
}
</style>