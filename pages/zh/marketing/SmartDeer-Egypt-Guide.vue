<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
    .countries-content
      h1.article-title 码住！中企出海必备指南【埃及篇】全新上线！
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '码住！中企出海必备指南【埃及篇】全新上线！',
  ogDescription: '全球智库力作——SmartDeer《中企出海国家/地区指南·埃及篇》正式上线，直击埃及人力市场机遇，解析跨境用工策略及劳动法合规核心，助力企业开拓埃及增长新引擎。      后续还将持续推出更多国家与地区攻略，用最实用的出海干货助您抢占全球商机。立即添加小助手，一键获取全系列最新指南，开启高效出海之旅！👇',
  ogSiteName: 'SmartDeer',
  description: '全球智库力作——SmartDeer《中企出海国家/地区指南·埃及篇》正式上线，直击埃及人力市场机遇，解析跨境用工策略及劳动法合规核心，助力企业开拓埃及增长新引擎。      后续还将持续推出更多国家与地区攻略，用最实用的出海干货助您抢占全球商机。立即添加小助手，一键获取全系列最新指南，开启高效出海之旅！👇'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '码住！中企出海必备指南【埃及篇】全新上线！'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '码住！中企出海必备指南【埃及篇】全新上线！';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2025/08/1111.jpg';
const flagImage = '';
const htmlContent = '<p class="has-text-align-center"><img loading="lazy" decoding="async" width="150" height="150" class="wp-image-1815" style="width: 150px;" src="https://blog.smartdeer.work/wp-content/uploads/2025/07/微信图片_20250723033911.png" alt=""></p><p class="has-text-align-center has-accent-4-color has-text-color has-link-color has-small-font-size wp-elements-ba9985d0813be8224e6665ae65850fef"><strong>如需详细了解活动内容或获取分享文件&nbsp;请添加小助手</strong></p><p><br>      全球智库力作——SmartDeer<strong>《中企出海国家/地区指南·埃及篇》</strong>正式上线，直击埃及人力市场机遇，解析跨境用工策略及劳动法合规核心，助力企业开拓埃及增长新引擎。      后续还将持续推出<strong>更多国家与地区攻略</strong>，用最实用的出海干货助您抢占全球商机。立即添加小助手，一键获取全系列最新指南，开启高效出海之旅！👇</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="1280" height="1681" src="https://blog.smartdeer.work/wp-content/uploads/2025/08/1111.jpg" alt="" class="wp-image-2053"/></figure><hr class="wp-block-separator has-alpha-channel-opacity"/><p><strong>【我们不止于此】🔜</strong><br><strong>       SmartDeer作为一站式人力资源服务平台，致力于服务企业出海，为出海企业提供完善的人力资源服务。</strong>SmartDeer将持续解锁更多出海指南，助您全球布局快人一步！</p><p>⭐️立即扫码添加<strong>【出海小助手】</strong></p><p>回复<strong>【埃及】</strong>免费领取出海指南</p><p>👇还能实时获取最新政策解读&amp;行业动态<br></p><p class="has-text-align-center"><img loading="lazy" decoding="async" width="150" height="150" class="wp-image-1815" style="width: 150px;" src="https://blog.smartdeer.work/wp-content/uploads/2025/07/微信图片_20250723033911.png" alt=""></p><p class="has-text-align-center has-accent-4-color has-text-color has-link-color has-small-font-size wp-elements-ba9985d0813be8224e6665ae65850fef"><strong>如需详细了解活动内容或获取分享文件&nbsp;请添加小助手</strong></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>