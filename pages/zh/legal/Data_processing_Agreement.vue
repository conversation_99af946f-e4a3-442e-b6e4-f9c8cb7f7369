<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-content
      h1.article-title SmartDeer数据处理协议
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: 'SmartDeer数据处理协议',
  ogDescription: 'SmartDeer数据处理协议',
  ogSiteName: 'SmartDeer',
  description: 'SmartDeer数据处理协议'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: 'SmartDeer数据处理协议'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = 'SmartDeer数据处理协议';
const bannerImage = '[object Object]';
const flagImage = '';
const htmlContent = '<p>上次更新时间：2025年7月10日</p><p>本数据处理协议（简称“DPA”）是客户与IDEAL-CAREERBRIDGE HOLDINGS (HK) LIMITED及其关联主体（分别称为“SmartDeer集团成员”，统称为“SmartDeer集团”） 之间的协议的一部分，以及根据本 DPA 定义的SmartDeer集团服务的使用者（“客户”）及其附属公司之间的协议。除非客户与SmartDeer集团另行签订了单独的书面协议，本补充协议构成该协议的一部分（在任何情况下，均称为“协议”）。</p><p>SmartDeer集团和客户将分别称为“一方”，合称为“双方”。客户在通过SmartDeer平台接受本补充协议或首次使用SmartDeer集团服务时，即表示同意受其条款的约束。</p><p>本补充协议适用于客户和SmartDeer集团根据适用的数据保护法律对本补充协议涵盖的个人数据进行处理，以提供服务。作为合同关系的一部分，双方承诺遵守适用于本补充协议所涵盖的个人数据处理的相关数据保护法律。</p><ul><li><strong>定义</strong><ul><li>“关联方”指（1）由IDEAL-CAREERBRIDGE HOLDINGS (HK) LIMITED直接或间接控制，或与其处于共同控制下的实体；（2）根据单独书面协议与SmartDeer集团合作运营的实体。</li></ul><ul><li> “数据保护法”是指任何适用的法律、法规或其他具有约束力的义务（包括任何和所有立法和/或监管修订或其后续规定），均会不时更新，这些法律、法规或义务来自中国、欧盟、欧洲经济区、瑞士、英国、美国、加拿大、澳大利亚或任何其他管辖区，用于管理或以其他方式适用于根据本协议处理的个人数据。</li></ul><ul><li> “个人数据”包括“个人数据”、“个人信息”、“个人身份信息”以及数据保护法定义的类似术语。</li></ul><ul><li> “处理”是指对个人数据执行的任何操作或一组操作，无论是否通过自动方式进行，例如收集、记录、组织、构建、存储、改编或修改、检索、咨询、使用、通过传输、传播或其他方式提供的披露、对齐或组合、限制、擦除或销毁。</li></ul><ul><li> “安全漏洞”是指对客户个人数据的任何意外或非法的获取、破坏、丢失、更改、未经授权的披露或访问。</li></ul><ul><li>“子处理者”是指SmartDeer集团聘用的处理客户个人数据的任何第三方。</li></ul><ul><li>“监管机构”是指根据数据保护法在其他适用司法管辖区设立的公共机构。</li></ul><ul><li>“标准合同条款”是指由相关主管机关批准的任何类型的标准化合同条款，例如在欧盟《通用数据保护条例》（GDPR）适用的情况下，指附于欧盟委员会 2021 年 6 月 4 日发布的关于根据欧洲议会和理事会条例（EU）2016/679 将个人数据传输至第三国的标准合同条款的实施决定（2021/914）中的合同条款（“欧盟标准合同条款”）。标准合同条款应通过引用方式纳入本数据处理协议，并构成其不可分割的一部分。</li></ul></li></ul><ul><li><strong>协议期限</strong></li><li>本协议的期限与协议的期限一致，协议的终止也将导致本 DPA 的终止。</li><li>此外，如果另一方违反数据保护法律中的法定或合同规定的数据保护条款，且涉及的签约方无法合理预期继续履行本 DPA，则在提前书面通知对方的情况下，本DPA的提前终止是允许的。</li><li>双方确认，无论何时、出于任何原因终止 DPA，都不免除他们在数据保护法律下关于个人数据的收集、处理和使用的义务。</li></ul><ul><li><strong>数据处理</strong></li><li>SmartDeer集团将作为数据控制者<strong>（</strong><strong>Controller</strong><strong>）</strong>，负责根据服务相关目的处理个人数据，具体用途如下：<ul><li>客户关系管理：用于管理与客户的关系。</li></ul><ul><li>核心业务运营：支持SmartDeer集团的核心业务，包括会计和税务申报。</li></ul><ul><li>安全与合规：用于检测、预防或调查安全事件、欺诈行为以及其他对服务的滥用或误用。</li></ul><ul><li>身份验证：用于验证用户身份。</li></ul><ul><li>法律义务履行：遵守SmartDeer集团的法律或监管义务。</li></ul><ul><li>其他允许的处理：在数据保护法允许的范围内，根据本DPA和SmartDeer集团隐私政策进行的其他处理。</li></ul></li><li><strong>当</strong>SmartDeer集团<strong>作为数据控制者（</strong><strong>Controller</strong><strong>）时，客户作为独立的数据控制者（</strong><strong>Controller</strong><strong>），各自根据适用的数据保护法承担独立的责任，双方并非共同控制者。</strong>SmartDeer集团将按照控制者身份对个人数据进行处理。当SmartDeer集团担任数据处理者（Processor）角色时，客户将作为控制者<strong>（</strong><strong>Controller</strong><strong>）</strong>，决定数据处理的目的和方式。客户应对其在SmartDeer集团期间，为管理与顾问的日常活动而进行的任何个人数据处理行为承担全部责任。为避免疑义，SmartDeer集团在客户直接授权下由顾问进行的数据处理行为中，既不是数据控制者，也不是数据处理者。</li><li>适用法律。各方应遵守所有适用于其并在履行本 DPA 时对其具有约束力的法律、规则和条例，包括数据保护法律。</li><li>处理说明。SmartDeer集团将代表客户并仅按照客户关于SmartDeer集团处理客户个人数据的书面指示处理客户个人数据，具体如下：<ul><li>按照协议和适用的订单执行处理；</li></ul><ul><li>由用户在客户授权使用 SmartDeer 服务时启动的处理；以及</li></ul><ul><li>为了遵守客户提供的其他书面合理指示（例如，通过电子邮件）而进行的处理，前提是这些指示与协议条款一致。</li></ul></li></ul><ul><li><strong>SmartDeer</strong><strong>集团员工和数据保护官</strong></li><li>SmartDeer员工。SmartDeer 将确保参与处理客户个人数据的员工知悉客户个人数据的保密性质，已接受有关其职责的适当培训，并已签署书面保密协议。SmartDeer 还将：<ul><li>采取商业上合理的措施，以确保参与处理客户个人数据的任何 SmartDeer 员工的可靠性；以及</li></ul><ul><li>确保 SmartDeer 对客户个人数据的访问权限仅限于根据本协议执行 SmartDeer 服务的员工。</li></ul></li><li>数据保护官。SmartDeer集团已为某些司法管辖区任命了数据保护官。可以通过**********************联系指定人员。</li></ul><ul><li><strong>数据控制者之间的数据传输条款</strong></li></ul><p>就双方根据本附加条款作为控制者处理的个人数据而言，每一方将：</p><ol><li>协助另一方履行其在数据保护法律下的义务；</li><li>迅速遵守另一方提出的任何合法请求，包括访问、复制个人数据，或对个人数据进行更正、转移或删除，只要该请求是使任一方履行数据保护法律义务所必需的；</li><li>根据本协议将个人数据视为保密信息，不“出售”或“共享”个人数据，或出于定向广告的目的处理个人数据，确保授权人员承担适当的保密义务；</li><li>一旦发现违反本条款的情况，应立即通知另一方，此时违约方应采取一切必要措施以纠正该违约行为；</li><li>双方均可就服务与第三方进行合作，并同意在与第三方相关的适用数据保护法律要求方面保持合规。双方应对其各自第三方的行为和疏忽承担与本 DPA 条款中该方应承担的责任相同的责任，除非协议中有其他规定。</li><li>在收到任何与个人数据处理相关或与任一方遵守本DPA中的数据保护法律相关的投诉（无论该投诉来自数据主体、监管机构或其他方）后，应毫不迟延地通知另一方，并在与任何此类投诉、通知或沟通相关的事宜上，向另一方提供合理的合作、信息和协助；</li></ol><ul><li><strong>数据控制者与处理者的数据传输条款</strong></li></ul><p>就SmartDeer 集团根据本DPA代表客户处理的个人数据而言，处理者将：</p><ol><li>仅根据客户的书面指示处理个人数据，除非法律要求以其他方式处理（在这种情况下，如果法律允许，应在处理前立即通知客户该要求）；</li><li>仅在必要范围内并以适当方式处理个人数据，以履行其在协议下的义务，包括为分析和开发目的处理已匿名化和聚合的数据；</li><li>保留其处理个人数据的记录，并确保记录的准确性；</li><li>根据本协议将个人数据视为保密信息，不“出售”或“共享”个人数据，或出于定向广告的目的处理个人数据，确保授权人员承担适当的保密义务；</li><li>及时遵守客户提出的任何合法请求，包括访问、复制、修改、转移或删除个人数据，只要该请求是客户履行其在数据保护法律下的义务所必需的，包括客户因数据主体的请求而产生的义务</li><li>仅在满足以下所有条件的情况下，方可将个人数据传输至欧盟经济区以外的地区：<ol><li>该方已就该传输建立了任何特定批准的数据传输保障措施（根据数据保护法律认可）；</li></ol><ol><li>数据主体在传输后仍享有可执行的权利和有效的法律救济途径；</li></ol><ol><li>该方为传输的个人数据提供了充分的保护水平（包括通过欧盟委员会的充分性认定）；以及</li></ol><ol><li>该方遵守有关传输的合理指示；</li></ol></li><li>在得知任何根据本补充协议处理的个人数据丢失、被毁、受损、被破坏或无法使用，或以其他方式遭受未经授权或非法处理，包括未经授权或非法访问或泄露（“个人数据泄露”）后，应在 48 小时内毫不迟延地通知客户；</li><li>在发生个人数据泄露时，应立即向客户提供全面的合作与协助，并提供处理器掌握的有关该个人数据泄露的所有信息，包括以下内容：<ol><li>个人数据泄露的可能原因和后果；</li></ol><ol><li>个人数据的类别以及涉及的数据主体的大致数量；以及</li></ol><ol><li>处理者为减轻任何损害所采取的措施；</li></ol></li><li>在法律允许的范围内，SmartDeer 将及时把从数据主体处收到的与其个人数据处理相关的任何投诉或请求（每个此类请求均为“数据主体请求”）通知客户。客户代表其自身，并在客户作为处理者时代表其控制者，授权 SmartDeer 回复向 SmartDeer 提出数据主体请求的任何数据主体，以确认 SmartDeer 已将该请求转发给客户。如果客户在使用 SmartDeer 服务的过程中，没有能力处理数据主体请求，SmartDeer 将在客户的要求下，在法律允许的范围内，并在数据保护法律要求回复此类数据主体请求的情况下，尽商业上合理的努力协助客户回复此类数据主体请求。在法律允许的范围内，客户将负责因 SmartDeer 提供此类协助而产生的任何费用。</li><li>未经客户要求或根据协议另有规定，不得将个人数据披露给任何第三方；</li><li>在客户遵守数据保护法律关于安全、数据泄露通知、数据保护影响评估以及与监管机构或主管部门咨询的义务方面，向客户提供合理的协助；</li><li>在正常营业时间内，向客户提供所有必要的信息，以便客户随时监督加工者对数据保护法律的遵守情况以及本附加条款下的义务履行情况。</li><li>在处理期限结束时，SmartDeer 集团应删除或将该个人数据返还给客户，并在那时删除或销毁现有的副本。如果返还或销毁不可行或受到法律、法规或规定的限制，SmartDeer 集团应采取措施阻止对这些个人数据的进一步处理（除非法律、法规或规定要求继续处理），并且应继续适当保护其持有、保管或控制下的剩余个人数据。</li></ol><ul><li><strong>子处理者</strong></li></ul><p>客户知悉并同意 SmartDeer 可聘用子处理者以提供 SmartDeer 服务。SmartDeer 将与每个子处理者签订书面协议，其中包含的数据保护义务不低于本 DPA 中的义务。客户可以通过发送电子邮件至**********************， 询问SmartDeer 服务的当前子处理者列表SmartDeer 将对其分包处理商的行为和疏忽承担责任，责任程度与 SmartDeer 根据本 DPA 条款直接履行每个子处理者的服务时应承担的责任相同，除非协议另有规定。</p><ul><li><strong>安全事件管理</strong></li><li>SmartDeer集团应采取适当的技术和组织措施，以应对相关风险，确保其根据本DPA处理的个人数据的安全性、保密性和完整性。</li><li>这些技术和组织措施应根据当前的技术状况和技术进步进行调整。在此方面，SmartDeer集团可以实施适当的替代措施，但这些措施不得为客户提供比本 协议中规定措施更低的安全保障水平。</li><li>第三方认证和审计。SmartDeer 使用外部审计师来验证其安全措施的充分性。根据客户在合理期限提出的书面请求，并且在遵守协议中规定的保密义务的前提下，SmartDeer集团将向非SmartDeer集团竞争对手的客户（或非 SmartDeer 竞争对手的客户的独立第三方审计师）提供 SmartDeer 当时最新的第三方审计或认证的副本（如适用）。</li><li>SmartDeer 在得知发生安全漏洞后，不得不适当地拖延，应及时通知客户。SmartDeer 将做出合理的努力来确定此类安全漏洞的原因，并采取 SmartDeer 认为必要且合理的措施，以便在补救措施在 SmartDeer 合理控制范围内的情况下，补救此类安全漏洞的原因。本协议中的义务不适用于由客户或客户用户引起的安全漏洞。</li><li>SmartDeer集团负有协助义务。为了使客户能够向监管机构或数据主体（如适用）通报安全漏洞，SmartDeer 将与客户合作并向其提供协助，包括向客户披露的有关安全漏洞的信息，同时考虑到处理的性质、 SmartDeer 掌握的信息以及披露信息的任何限制，例如保密性。</li></ul><ul><li><strong>客户个人数据的返还和删除</strong></li></ul><p>根据客户在协议终止时或之前提出的要求，Smart Deer集团将合理配合客户，以便于从 SmartDeer集团的系统中导出此类客户个人数据，之后可以从同一系统中删除任何和所有剩余的客户个人数据，除非法律要求或禁止进一步保留。</p><ul><li><strong>数据传输</strong></li></ul><p>客户知悉，SmartDeer 服务可能涉及客户个人数据的跨境传输。如果 SmartDeer集团从事客户个人数据的任何跨境处理，或将任何客户个人数据传输到提供此类客户个人数据所在国家/地区以外的任何国家/地区，SmartDeer集团将遵守数据保护法律。在数据保护法律要求的范围内，SmartDeer集团将确保在从事从一个国家/地区到另一个国家/地区的任何后续客户个人数据传输之前，已建立合法的数据传输机制，并且在适用于传输的范围内，遵守规定的特定司法管辖区的条款。</p><ul><li><strong>联系与沟通</strong></li></ul><p>作为本协议签约方的客户将继续负责协调与SmartDeer集团在本 DPA 下的所有联系，并有权代表其授权关联公司就本 DPA 进行和接收任何通信。</p><ul><li><strong>授权关联公司的权利</strong></li><li>如果授权关联公司成为与 SmartDeer 签订的 DPA 的一方，则在适用数据保护法律要求的范围内，它将有权行使本 DPA 项下的权利并寻求补救，但须遵守以下规定：<ul><li>除非适用的数据保护法律要求授权关联公司直接对Smart Deer集团行使本 DPA 项下的权利或寻求任何补救，否则双方同意 (i) 只有作为协议签约方的客户才能代表授权关联公司行使任何此类权利或寻求任何此类补救，并且 (ii) 作为协议签约方的客户将根据本 DPA 行使任何此类权利，而不是为每个授权关联公司单独行使，而是为其自身及其所有授权关联公司以合并的方式行使。</li></ul><ul><li>双方同意，作为协议签约方的客户在对与保护客户个人数据相关的程序进行现场审核时，应采取一切合理措施，通过在合理可行的范围内，将代表其自身及其所有授权关联公司进行的多次审核请求合并为一次审核，从而限制对SmartDeer集团及其子处理者的任何影响。</li></ul></li></ul><ul><li><strong>责任限制</strong></li></ul><p>在数据保护法律允许的范围内，因本 DPA 引起或与之相关引起的每一方及其所有关联公司的责任，以及授权关联公司与SmartDeer集团之间的所有 DPA，无论是在合同、侵权行为或任何其他责任下的，均受SmartDeer集团客户协议的责任限制条款的约束，并且该条款中对一方责任的任何提及均指该方及其所有关联公司在协议和所有DPA下的总责任。为避免疑义，SmartDeer 及其关联公司对客户及其所有授权关联公司因协议和所有 DPA 引起或与之相关的所有索赔的总责任，将适用于协议和根据本协议建立的所有 DPA 下的所有索赔，包括客户及其所有授权关联公司的索赔，并且尤其不应理解为单独地和分别地适用于客户和/或作为任何此类 DPA 的合同方的任何授权关联公司。</p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>