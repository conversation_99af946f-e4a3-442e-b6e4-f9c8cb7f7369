<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-content
      h1.article-title 阿联酋&沙特6月放假安排
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '阿联酋&沙特6月放假安排',
  ogDescription: '放假通知  宰牲节 & 伊斯兰新年放假安排（阿联酋与沙特） 根据阿联酋和沙特政府公布的法定节 […]',
  ogSiteName: 'SmartDeer',
  description: '放假通知  宰牲节 & 伊斯兰新年放假安排（阿联酋与沙特） 根据阿联酋和沙特政府公布的法定节 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '阿联酋&沙特6月放假安排'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '阿联酋&沙特6月放假安排';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2025/07/微信图片_20250731160927.png';
const flagImage = '';
const htmlContent = '<h2 class="wp-block-heading has-text-align-center has-base-2-background-color has-background"><strong><br>放假通知&nbsp;</strong></h2><p><strong>宰牲节 &amp; 伊斯兰新年放假安排（阿联酋与沙特）</strong></p><p></p><p>根据阿联酋和沙特政府公布的法定节假日安排，相关国家政府部门将于以下日期放假：<strong></strong></p><p><strong>6月</strong><strong>阿联酋</strong><strong>放假安排：</strong></p><p>2025年<strong>6月5日（星期四）</strong>至<strong>6月8日（星期日</strong>）宰牲节 Eid Al Adha</p><p>2025年<strong>6月26日（星期四）</strong>伊斯兰新年 Islamic New Year一二三四五六日</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="801" height="438" src="https://blog.smartdeer.work/wp-content/uploads/2025/07/微信图片_20250731161034.png" alt="" class="wp-image-1926"/></figure><p><strong>6月</strong><strong>沙特放假安排：</strong></p><p>2025年<strong>6月5日（星期四）</strong>至<strong>6月10日（星期二</strong>）宰牲节 Eid Al Adha</p><p>2025年<strong>6月26日（星期四）</strong>伊斯兰新年 Islamic New Year一二三四五六日</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="799" height="455" src="https://blog.smartdeer.work/wp-content/uploads/2025/07/微信图片_20250731160927.png" alt="" class="wp-image-1925"/></figure><hr class="wp-block-separator has-alpha-channel-opacity"/><hr class="wp-block-separator has-alpha-channel-opacity"/><p>⏰放假期间，当地政府部门将暂停日常运营和服务（如工商注册、文件认证、移民处理、银行业务等），建议您提前安排相关事项并及时与我们沟通，以避免延误。</p><p>⚠️<strong>SmartDeer国内团队</strong>在假期期间将<strong>照常办公</strong>，<strong>SmartDeer阿联酋及沙特本地团队</strong>将按各自国家节假日安排<strong>休假</strong>。如您在此期间有任何问题或紧急需求，欢迎随时联系 SmartDeer国内团队，我们将全力协助您！</p><p>       SmartDeer，作为全球领先的人力资源一站式服务商，为中国出海企业解决包括全球招（免费链接优质项目和全球人才的招聘平台SmartDeer APP）全球雇（全球合规雇佣与薪酬发放等）。SmartDeer拥有自营的华人当地团队，分布在20多个国家和地区，可覆盖150+国家和地区的人力资源服务。</p><p></p><p></p><p class="has-text-align-center"><img loading="lazy" decoding="async" width="150" height="150" class="wp-image-1815" style="width: 150px;" src="https://blog.smartdeer.work/wp-content/uploads/2025/07/微信图片_20250723033911.png" alt=""></p><p class="has-text-align-center has-accent-4-color has-text-color has-link-color has-small-font-size wp-elements-7d86a954db25d9062e5e4a5b66bb3c48"><strong>扫码联系我们</strong> 出海即刻启航</p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>