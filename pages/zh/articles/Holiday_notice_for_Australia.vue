<template lang="pug">
.contries-page
  site-header(lang="zh" :showContactUs="false")

  .countries
    .countries-content
      h1.article-title 澳大利亚放假通知
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '澳大利亚放假通知',
  ogDescription: '放假通知  King’s Birthday 澳大利亚公共假日 根据澳大利亚各州政府公布的节假日安排， […]',
  ogSiteName: 'SmartDeer',
  description: '放假通知  King’s Birthday 澳大利亚公共假日 根据澳大利亚各州政府公布的节假日安排， […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '澳大利亚放假通知'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '澳大利亚放假通知';
const bannerImage = '';
const flagImage = '';
const htmlContent = '<h2 class="wp-block-heading has-text-align-center" id="block-c1f9d504-5520-40cc-9cd4-868c74922d03"><strong><br>放假通知&nbsp;</strong></h2><p><strong>King’s Birthday 澳大利亚公共假日</strong></p><p>根据澳大利亚各州政府公布的节假日安排，<strong>除昆士兰州（Queensland）</strong>和<strong>西澳大利亚州（Western Australia）外</strong>，<strong>全国大部分地区</strong>将在：</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="816" height="271" src="https://blog.smartdeer.work/wp-content/uploads/2025/07/image-7.png" alt="" class="wp-image-1944"/></figure><p>⏰放假期间，大部分澳洲政府机构与银行将暂停运营，可能影响公司注册、文件办理、银行业务等服务。如您有相关需求，建议提前安排并与我们联系，以避免耽误。</p><p>⚠️ SmartDeer团队将根据本地假期安排适时调整工作进度。如有任何问题或紧急事项，请随时联系我们的中国团队协助处理。</p><p>      SmartDeer，作为全球领先的人力资源一站式服务商，为中国出海企业解决包括全球招（免费链接优质项目和全球人才的招聘平台SmartDeer APP）全球雇（全球合规雇佣与薪酬发放等）。SmartDeer拥有自营的华人当地团队，分布在20多个国家和地区，可覆盖150+国家和地区的人力资源服务。</p><p class="has-text-align-center"><img loading="lazy" decoding="async" width="150" height="150" class="wp-image-1815" style="width: 150px;" src="https://blog.smartdeer.work/wp-content/uploads/2025/07/微信图片_20250723033911.png" alt=""></p><p class="has-text-align-center has-accent-4-color has-text-color has-link-color has-small-font-size wp-elements-7d86a954db25d9062e5e4a5b66bb3c48"><strong>扫码联系我们</strong> 出海即刻启航</p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>