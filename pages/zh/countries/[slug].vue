<template lang="pug">
.country-detail-page
  site-header(lang="zh")

  main.main-content
    .container
      //- Breadcrumbs for navigation
      nav.breadcrumbs
        NuxtLink(to="/zh") 首页
        span /
        NuxtLink(to="/zh/countries") 国家指南
        span /
        span {{ countryData.name }}

      //- Main title
      h1.page-title {{ pageTitle }}

      //- Introduction Section
      section.section
        h2.section-title 在 {{ countryData.name }} 雇佣员工
        p.section-content {{ countryData.introduction }}

      //- Key Facts Section
      section.section
        h2.section-title {{ countryData.name }} 关键信息概览
        ul.key-facts-list
          li(v-for="fact in countryData.keyFacts" :key="fact.title")
            strong {{ fact.title }}:
            span {{ fact.value }}

      //- Compliance Section
      section.section
        h2.section-title 雇佣合规要点
        p.section-content {{ countryData.compliance.summary }}
        ul.compliance-details
          li(v-for="detail in countryData.compliance.details" :key="detail") {{ detail }}

      //- Benefits Section
      section.section
        h2.section-title {{ countryData.name }} 的员工福利
        p.section-content {{ countryData.benefits }}

      //- Call to Action
      .cta-section
        p 准备在 {{ countryData.name }} 开启您的业务？
        button.cta-button(@click="showContactForm = true") 获取专家咨询

  //- Contact Form Dialog
  client-only
    el-dialog(v-model="showContactForm" title="获取关于在 {{ countryData.name }} 雇佣的咨询" :width="600")
      contact-us-form(lang="zh" @submit="submitSuccess")

  site-footer(lang="zh" @contact-us="() => showContactForm = true")

</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElDialog, ElMessage } from 'element-plus'

definePageMeta({ layout: 'basic' })

const route = useRoute()
const slug = route.params.slug as string

// --- Mock Data ---
// In a real application, this data would be fetched from a CMS or API
// based on the 'slug'.
const countriesData = {
  japan: {
    name: '日本',
    introduction: '日本是亚洲领先的经济体之一，拥有高技能的劳动力和庞大的消费市场。通过我们的EOR服务，您可以快速进入日本市场，而无需设立本地实体。',
    keyFacts: [
      { title: '首都', value: '东京' },
      { title: '货币', value: '日元 (JPY)' },
      { title: '官方语言', value: '日语' },
      { title: '主要产业', value: '汽车、电子、机械' },
    ],
    compliance: {
      summary: '日本的劳动法非常严格，注重保护员工权利。合同必须明确规定工作条件、薪酬和解雇条款。',
      details: [
        '标准工作周为40小时。',
        '员工享有至少10天的带薪年假。',
        '必须为员工缴纳社会保险和劳动保险。',
      ],
    },
    benefits: '日本的法定福利包括健康保险、养老金和失业保险。许多公司还提供交通补贴、住房津贴和年度奖金等额外福利。',
  },
  singapore: {
    name: '新加坡',
    introduction: '新加坡是全球商业和金融中心，以其亲商环境和战略位置而闻名。在这里招聘人才可以作为进入东南亚市场的门户。',
    keyFacts: [
      { title: '首都', value: '新加坡' },
      { title: '货币', value: '新加坡元 (SGD)' },
      { title: '官方语言', value: '英语、马来语、华语、泰米尔语' },
      { title: '主要产业', value: '金融、科技、贸易、制造业' },
    ],
    compliance: {
      summary: '新加坡的雇佣法律相对灵活，但《雇佣法》为大多数员工提供了核心保护。',
      details: [
        '没有法定的最低工资���',
        '标准工作周通常为44小时。',
        '雇主必须为符合条件的员工缴纳中央公积金（CPF）。',
      ],
    },
    benefits: '除了中央公积金（CPF），没有强制性的额外福利。然而，提供补充医疗保险和灵活工作安排是吸引人才的常见做法。',
  },
}

const countryData = computed(() => countriesData[slug] || {
  name: '未知国家',
  introduction: '暂无该国家/地区的详细信息。',
  keyFacts: [],
  compliance: { summary: '', details: [] },
  benefits: '',
})

// --- SEO Optimization ---
const pageTitle = computed(() => `在日本雇佣员工 | 日本EOR名义雇主服务 | SmartDeer`)
const pageDescription = computed(() => `通过SmartDeer的EOR服务，轻松在日本招聘顶尖人才。我们提供合规的雇佣合同、薪酬管理和税务解决方案，助您快速拓展日本业务。`)

useSeoMeta({
  title: pageTitle,
  description: pageDescription,
  ogTitle: pageTitle,
  ogDescription: pageDescription,
  twitterCard: 'summary_large_image',
})

// --- Component Logic ---
const showContactForm = ref(false)

function submitSuccess() {
  showContactForm.value = false
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
</script>

<style lang="scss" scoped>
.country-detail-page {
  background-color: #f9f9f9;
}

.main-content {
  padding: 40px 0;
}

.container {
  width: 1000px;
  margin: 0 auto;
  background-color: #fff;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.breadcrumbs {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;

  a {
    color: #FF7F00;
    text-decoration: none;
    &:hover {
      text-decoration: underline;
    }
  }

  span {
    margin: 0 8px;
  }
}

.page-title {
  font-size: 42px;
  font-weight: 600;
  margin-bottom: 30px;
  color: #333;
}

.section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 28px;
  font-weight: 500;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #FF7F00;
  display: inline-block;
}

.section-content {
  font-size: 16px;
  line-height: 1.8;
  color: #555;
}

.key-facts-list, .compliance-details {
  list-style: none;
  padding-left: 0;
  font-size: 16px;
  line-height: 1.8;

  li {
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;

    &::before {
      content: '•';
      color: #FF7F00;
      position: absolute;
      left: 0;
      font-weight: bold;
    }
  }

  strong {
    margin-right: 8px;
    color: #333;
  }
}

.cta-section {
  text-align: center;
  padding: 40px;
  background-color: #FFF6EC;
  border-radius: 8px;

  p {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 20px;
  }

  .cta-button {
    background-color: #FF7F00;
    color: #fff;
    border: none;
    padding: 15px 30px;
    font-size: 18px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: #e67300;
    }
  }
}
</style>
