<template lang="pug">
.index-page
  img.loading(src="~/assets/images/loading-min.svg")
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import langTool from '~/assets/utils/lang'
definePageMeta({ layout: 'blank' })

// Basic SEO for the redirect page
useHead({
  htmlAttrs: { lang: 'en' },
  title: 'SmartDeer - 全球雇佣&全球招聘解决方案',
  meta: [
    { name: 'description', content: 'SmartDeer提供全球雇佣、全球招聘一站式解决方案。专业EOR服务、国际薪酬管理、合规支持，助力中国企业海外扩张，覆盖150+国家和地区。立即获取免费咨询，开启您的全球化之旅。' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },

    // Canonical to English version as default
    { name: 'canonical', content: 'https://smartdeer.work/en/' }
  ],
  link: [
    { rel: 'canonical', href: 'https://smartdeer.work/en/' },

    // Hreflang for multilingual SEO
    { rel: 'alternate', hreflang: 'en', href: 'https://smartdeer.work/en/' },
    { rel: 'alternate', hreflang: 'zh-CN', href: 'https://smartdeer.work/zh/' },
    { rel: 'alternate', hreflang: 'ja-JP', href: 'https://smartdeer.work/ja/' },
    { rel: 'alternate', hreflang: 'x-default', href: 'https://smartdeer.work/en/' }
  ]
})

onMounted(() => {
  // 使用新的referrer检测逻辑
  const { detectAndStoreReferrer } = useReferrerDetection()
  const referrerInfo = detectAndStoreReferrer()
  
  console.log('🎯 首页检测到的referrer信息:', referrerInfo)
  
  // 执行语言检测和跳转，并传递referrer信息
  langTool.indexGuide(referrerInfo)
})

</script>

<style lang="scss" scoped>
.index-page {
  display: flex;
  min-height: 100vh;
  justify-content: center;
  align-items: center;
}

.loading {
  animation: rotate .5s steps(1) infinite;
  width: 40px;
  opacity: .3;
}


@keyframes rotate {
  0% {
    transform: rotate(0deg)
  }

  12.5% {
    transform: rotate(45deg)
  }

  25% {
    transform: rotate(90deg)
  }

  37.5% {
    transform: rotate(135deg)
  }

  50% {
    transform: rotate(180deg)
  }

  62.5% {
    transform: rotate(225deg)
  }

  75% {
    transform: rotate(270deg)
  }

  87.5% {
    transform: rotate(315deg)
  }
}
</style>