<template lang="pug">
.contries-page
  site-header(lang="ja" :showContactUs="false")

  .countries
    .countries-content
      h1.article-title 企业出海有一个致命问题，出海企业一定要规避！
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="ja" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="ja" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '企业出海有一个致命问题，出海企业一定要规避！',
  ogDescription: '企业出海有一个致命问题，就是很多中国企业出出海到海外，比如在中东沙特这些小语种国家，遇到最大的问题就是这个人才 […]',
  ogSiteName: 'SmartDeer',
  description: '企业出海有一个致命问题，就是很多中国企业出出海到海外，比如在中东沙特这些小语种国家，遇到最大的问题就是这个人才 […]'
})
useHead({
  htmlAttrs: {
    lang: 'ja-JP'
  },
  title: '企业出海有一个致命问题，出海企业一定要规避！'
})
const status = reactive({
  showForm: false
})
const langSimple = 'ja';
const pageTitle = '企业出海有一个致命问题，出海企业一定要规避！';
const bannerImage = '';
const flagImage = '${COUNTRY_FLAT_IMAGE_URL}';
const htmlContent = '<p>企业出海有一个致命问题，就是很多中国企业出出海到海外，比如在中东沙特这些小语种国家，遇到最大的问题就是这个人才，在当地招聘一个懂中文又懂当地语的这个人非常难，现在竞争非常激烈，而且我们中国企业很多不熟悉当地的法律法规，所以说在当地招聘人才是一个非常大的痛点，如何解决这个海外专业人才的这个痛点呢，我觉得现在刚好一个很好的机会，就是我们smartdeer在做这个留学生回国就业的这个项目，我们smartdeer为全球150多个国家和地区的企业打交道，提供专业全面的HRO服务，帮助企业吸引招纳全球优秀人才，从校园招聘到中高端人才，让更多人才与全球优秀项目产生连接，如果你的企业已经到了海外，如果你现在发现没有人才，可以关注我们smartdeer。</p>';
function submitSuccess() {
  ElMessage.success('お問い合わせを受け付けました。できるだけ早くご連絡いたします。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>