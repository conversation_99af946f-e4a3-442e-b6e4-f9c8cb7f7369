<template lang="pug">
mixin header-nav
  section.header-nav
    NuxtLink(to="/")
      figure.logo
        img(src="~/assets/images/aboutus/sd_logo.png" alt="~/assets/images/aboutus/sd_logo.png")
    .extra
      .contact-us(@click="()=>{status.showForm = true}")
        .text お問い合わせ
      client-only
        el-dropdown.language-selector
          .text(style="color: #000;") 中 / EN / 日
          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(@click="switchLang('zh')") 中文
              el-dropdown-item(@click="switchLang('en')") English
              el-dropdown-item(@click="switchLang('ja')") 日本語

mixin company-profile 
  section.company-profile
    .section-title
      h2.title グローバルHRソリューションのパートナー

    .profile-content
      .figure
        img(src="~/assets/images/aboutus/background.jpeg" alt="~/assets/images/aboutus/background.jpeg")
        h3 会社概要
        p SmartDeerは、グローバル採用・雇用ソリューションを一体的に提供する「HRサービス＆SaaS」プラットフォームです。 SmartDeerはZixin Capitalのインキュベーションにより、WeLight Fund、WeWork、Hash Globalなどから出資を受けています。 地域の壁を越えて世界中の人材を迅速に採用し、入社から退職、給与、税務、福利厚生まで、雇用ライフサイクル全体を包括的に管理します。 プラットフォームは、各プロセスにおいてコンプライアンスと効率性を担保し、グローバルチームの管理をシンプルにします。
        p また、SmartDeerは国際的に権威ある情報セキュリティマネジメントシステム「ISO 27001」認証を取得済みであり、お客様のデータ安全・プライバシーを徹底保護し、信頼性の高いサービスを提供しています。
      .figure.right
        img(src="~/assets/images/aboutus/globalization.jpeg" alt="~/assets/images/aboutus/globalization.jpeg")
        h3 グローバル展開
        p 香港とシンガポールに拠点を持つ当社は、グローバル戦略上の強みを享受しつつ、各地域のデータ保管・処理規制にも完全対応しています。米国、英国、UAE、サウジアラビア、オーストラリア、日本、韓国、タイ、マレーシア、インドネシア、フィリピン、ベトナム、中国本土、メキシコ、ブラジルなどに現地法人・支店を設置しているほか、自社ネットワークとパートナーネットワークを通じて、150以上の国と地域でサービスを展開しています。
      .figure
        img(src="~/assets/images/index/eor.webp" alt="~/assets/images/index/eor.webp")
        h3 私たちのチーム
        p SmartDeerは、世界10カ国以上に150名以上の従業員を擁し、多言語でのサポートを提供しています。特に中国語と英語のバイリンガル対応に加え、現地語にも強みを持ち、各地域のニーズに柔軟に対応可能です。各国の法規制にも深く精通しており、地域に根ざした専門的なサービスとグローバル対応の両立を実現しています。
      .figure.right
        img(src="~/assets/images/aboutus/scope.jpeg" alt="~/assets/images/aboutus/scope.jpeg")
        h3 サービス範囲
        p 当社は、採用支援、グローバル雇用コンプライアンス、ビザ取得、給与管理、福利厚生および税務対応を含む包括的なHRソリューションを提供しています。SmartDeerの強力なグローバルHR SaaSシステムにより、企業は複雑なグローバル人事業務を効率化し、プロセスを簡素化、コンプライアンスリスクを軽減できます。プラットフォームを活用することで、企業は世界中での人材採用・入退社・有給休暇・給与・税務を一括管理し、グローバルなビジネス成功を支援します。

mixin profession
  section.profession
    .profession-list
      .profession-item
        figure 
          img(src="~/assets/images/aboutus/earth.svg" alt="~/assets/images/aboutus/earth.svg")
        .content
          .title グローバルな連携オフィス体制
          .desc アメリカ、イギリス、UAE、サウジアラビア、オーストラリア、シンガポール、日本、韓国、タイ、マレーシア、インドネシア、フィリピン、ベトナム、中国本土、メキシコ、香港などに現地のオペレーションチームを展開しています。

      .profession-item
        figure

          img(src="~/assets/images/aboutus/team.svg" alt="~/assets/images/aboutus/team.svg")
        .content
          .title 10年以上の実務経験を持つ専門チーム
          .desc 世界各国出身の人材が在籍し、10年以上の本社人事部での勤務経験やHRコンサルティング実績を有する専門家が多数在籍しています。

      .profession-item
        figure 
          img(src="~/assets/images/aboutus/cover.svg" alt="~/assets/images/aboutus/cover.svg")
        .content
          .title サービス範囲の完全網羅
          .desc グローバルな人材採用、正社員雇用、フレキシブルな契約雇用、人事業務のBPO（業務委託）まで、あらゆるHRニーズに対応しています。

mixin global-office
  section.global-office
    .section-title
      h2.title グローバル拠点一覧

    .office
      .office-list
        .office-item
          figure 
            img(src="~/assets/images/aboutus/hongkong.webp" alt="~/assets/images/aboutus/hongkong.webp")
          .content
            .title 香港
            .location 住所: Room 705-706, 7/F., China Insurance Group Building, No. 141 Des Voeux Road Central, Central, Hong Kong
        .office-item
          figure
            img(src="~/assets/images/aboutus/singapore.webp" alt="~/assets/images/aboutus/singapore.webp")
          .content
            .title シンガポール
            .location 住所：3 Fraser Street, #5-25 Duo Tower, Singapore (189352)
        .office-item
          figure
            img(src="~/assets/images/aboutus/california.jpeg" alt="~/assets/images/aboutus/california.jpeg")
          .content
            .title アメリカ
            .location 住所：15025 PROCTOR AVAVE CITY OF INDUSTRY, Y, CA CA 91746
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/uk.jpeg" alt="~/assets/images/aboutus/uk.jpeg")
          .content
            .title イギリス
            .location 住所：69 Aberdeen Avenue, Cambridge, England, CB2 8DL
        .office-item
          figure
            img(src="~/assets/images/aboutus/australia.jpeg" alt="~/assets/images/aboutus/australia.jpeg")
          .content
            .title オーストラリア
            .location 住所：135 KING STREET, SYDNEY, NSW 2000
        .office-item
          figure
            img(src="~/assets/images/aboutus/aue.jpeg" alt="~/assets/images/aboutus/aue.jpeg")
          .content
            .title アラブ首長国連邦
            .location 住所：Office328,BlockB,Business Village, Deira, Dubai, UAE
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/japan.jpeg" alt="~/assets/images/aboutus/japan.jpeg")
          .content
            .title 日本
            .location 住所：神奈川県横浜市中区山下町98GSハイム山下町4階403号室
        .office-item
          figure
            img(src="~/assets/images/aboutus/korea.jpeg" alt="~/assets/images/aboutus/korea.jpeg")
          .content
            .title 韓国
            .location 住所：서울특별시 중랑구 동일로825，2층 205호 (중화동)
        .office-item
          figure
            img(src="~/assets/images/aboutus/thailand.jpeg" alt="~/assets/images/aboutus/thailand.jpeg")
          .content
            .title タイ
            .location 住所：11 / 152-153 Room No. GDC 101, 1st Floor, Moo 5, Kookong District, Lam Luka District, Pathum Thani Province
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/malaysia.jpeg" alt="~/assets/images/aboutus/malaysia.jpeg")
          .content
            .title マレーシア
            .location 住所：332F-­2, HARMONY SQUARE, JALAN PERAK 11600 JELUTONG PULAU PINANG MALAYSIA
        .office-item
          figure
            img(src="~/assets/images/aboutus/indonesia.jpeg" alt="~/assets/images/aboutus/indonesia.jpeg")
          .content
            .title インドネシア
            .location 住所：Gedung Wirausaha Lantai 1 Unit 104, Jalan HR Rasuna Said Kav. C-5,Desa/Kelurahan Karet, Kec. Setiabudi, Kota Adm. Jakarta Selatan, ProvinsiDKI Jakarta
        .office-item
          figure
            img(src="~/assets/images/aboutus/philippines.jpeg" alt="~/assets/images/aboutus/philippines.jpeg")
          .content
            .title フィリピン
            .location 住所：UNIT 25D 2ND FLOOR ZETA II BLDG.191 SALCEDO ST., SAN LORENZO,CITY OF MAKATI,FOURTH DIRECT, NATIONAL CAPITAL REGION (NCR), 1223
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/vietnam.jpeg" alt="~/assets/images/aboutus/vietnam.jpeg")
          .content
            .title ベトナム
            .location 住所：Room(s):28 ,Level 14, Saigon Centre Tower 1, No. 65 Le Loi Street, Ben Nghe Ward, District 1, Ho Chi Minh City,Vietnam
        .office-item
          figure
            img(src="~/assets/images/aboutus/beijing.webp" alt="~/assets/images/aboutus/beijing.webp")
          .content
            .title 北京
            .location 住所：4F, Wangfu International Center wework,Wangfujing, Dongcheng District, Beijing

        .office-item
          figure
            img(src="~/assets/images/aboutus/shanghai.webp" alt="~/assets/images/aboutus/shanghai.webp")
          .content
            .title 上海
            .location 住所：Floor 12, Huirong Building, No. 535, Caoyang Road, Putuo District, Shanghai Chengdu: 6F-K0
      .office-list
        .office-item
          figure
            img(src="~/assets/images/aboutus/shenzhen.webp" alt="~/assets/images/aboutus/shenzhen.webp")
          .content
            .title 深圳
            .location 住所：0701-D021, Port Building, Ocean Freight Center, No. 59, Linhai Avenue, Nanshan Street, Shenzhen-Hong Kong Modern Service Industry Cooperation Zone in Qianhai, Shenzhen
        .office-item
          figure
            img(src="~/assets/images/aboutus/chengdu.webp" alt="~/assets/images/aboutus/chengdu.webp")
          .content
            .title 成都
            .location 住所：6F-K0063, Lei Shing Hong Plaza, No. 5 Hangtian Road, Chenghua District, Chengdu City, Sichuan Province
        .office-item
          figure
            img(src="~/assets/images/aboutus/hangzhou.webp" alt="~/assets/images/aboutus/hangzhou.webp")
          .content
            .title 杭州
            .location 住所：Room 205, Building 2, No. 8-1, Longquan Road, Qianjie Street, Yuhang District, Hangzhou City, Zhejiang Province

mixin contact-us
  .contact-form
    //- 吃瓜就要付出代价，否则影响页面跳转
    //- https://github.com/element-plus/element-plus/pull/9731
    client-only
      el-dialog(v-model="status.showForm" title="" :width="600")
        contact-us-form(@submit="submitSuccess" lang="ja")

.page-about-us
  .header
    +header-nav
    +company-profile

  +profession
  +global-office

  +contact-us

  SiteFooter(lang="ja" @contact-us="()=>{ status.showForm = true }")

</template>

<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
definePageMeta({ layout: 'basic' })
useHead({
  htmlAttrs: { lang: 'ja-JP' },
  title: 'SmartDeerについて - グローバル人事ソリューションプロバイダー',
  meta: [
    // Basic SEO
    { name: 'description', content: 'SmartDeerについて詳しく知る。グローバル人事ソリューションの業界リーダーとして、EORサービス、国際採用、給与管理を150カ国以上で提供し、世界各地にオフィスを構えています。' },
    { name: 'keywords', content: 'SmartDeerについて, グローバル人事会社, 国際雇用サービス, EORプロバイダー, グローバル採用会社, 人事ソリューション, 国際給与管理, 海外展開支援, グローバルコンプライアンス' },
    { name: 'author', content: 'SmartDeer' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },

    // Canonical and hreflang
    { name: 'canonical', content: 'https://smartdeer.work/ja/aboutus' },

    // Open Graph
    { property: 'og:title', content: 'SmartDeerについて - グローバル人事ソリューションプロバイダー' },
    { property: 'og:description', content: 'SmartDeerについて詳しく知る。グローバル人事ソリューションの業界リーダーとして、EORサービス、国際採用、給与管理を150カ国以上で提供。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'https://smartdeer.work/ja/aboutus' },
    { property: 'og:image', content: 'https://smartdeer.work/images/aboutus/company-overview-ja.png' },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:locale', content: 'ja_JP' },

    // Twitter Card
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'SmartDeerについて - グローバル人事ソリューション' },
    { name: 'twitter:description', content: 'SmartDeerについて詳しく知る。グローバル人事ソリューションの業界リーダーとして、EORサービス、国際採用、給与管理を150カ国以上で提供。' },
    { name: 'twitter:image', content: 'https://smartdeer.work/images/aboutus/company-overview-ja.png' },
    { name: 'twitter:image:alt', content: 'SmartDeerグローバルオフィスとチーム' }
  ],
  link: [
    // Canonical URL
    { rel: 'canonical', href: 'https://smartdeer.work/ja/aboutus' },

    // Hreflang for multilingual SEO
    { rel: 'alternate', hreflang: 'en', href: 'https://smartdeer.work/en/aboutus' },
    { rel: 'alternate', hreflang: 'zh-CN', href: 'https://smartdeer.work/zh/aboutus' },
    { rel: 'alternate', hreflang: 'ja-JP', href: 'https://smartdeer.work/ja/aboutus' },
    { rel: 'alternate', hreflang: 'x-default', href: 'https://smartdeer.work/en/aboutus' }
  ],
  script: [
    // Structured Data - AboutPage
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "AboutPage",
        "name": "SmartDeerについて",
        "description": "SmartDeerについて詳しく知る。グローバル人事ソリューションプロバイダーとして包括的な国際雇用サービスを提供",
        "url": "https://smartdeer.work/ja/aboutus",
        "inLanguage": "ja-JP",
        "mainEntity": {
          "@type": "Organization",
          "name": "SmartDeer",
          "description": "グローバル人事ソリューション・雇用サービスプロバイダー",
          "url": "https://smartdeer.work",
          "logo": "https://smartdeer.work/images/logo.png",
          "foundingDate": "2020",
          "numberOfEmployees": "150+",
          "address": [
            {
              "@type": "PostalAddress",
              "addressLocality": "香港",
              "addressRegion": "香港",
              "streetAddress": "Room 705-706, 7/F., China Insurance Group Building, No. 141 Des Voeux Road Central, Central",
              "addressCountry": "HK"
            },
            {
              "@type": "PostalAddress",
              "addressLocality": "シンガポール",
              "addressRegion": "シンガポール",
              "streetAddress": "3 Fraser Street, #5-25 Duo Tower",
              "postalCode": "189352",
              "addressCountry": "SG"
            }
          ],
          "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "availableLanguage": ["Japanese", "English", "Chinese"]
          },
          "sameAs": [
            "https://www.linkedin.com/company/smartdeer-global/"
          ],
          "areaServed": "世界全体",
          "serviceArea": {
            "@type": "GeoCircle",
            "name": "グローバルカバレッジ - 150カ国以上"
          }
        }
      })
    }
  ]
})

const status = reactive({
  showForm: false
})

function switchLang(lang) {
  langTool.swithLang(lang, '/aboutus')
}

function submitSuccess() {
  status.showForm = false
  ElMessage.success('お問い合わせありがとうございます。担当者よりご連絡いたします。')
}
</script>

<style lang="scss" scoped>
@import url('@/assets/styles/en.scss');

.page-about-us {
  font-family: Helvetica;

  section {
    .section-title {
      text-align: center;

      h2.title {
        font-weight: bold;
        font-size: 48px;
        position: relative;
        display: inline-block;
        line-height: 58px;

        &::before {
          content: '';
          display: block;
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          background-color: #FF8600;
          height: 8px;
          z-index: -1;
        }
      }
      
      p {
        font-size: 18px;
        color: #999999;
        letter-spacing: 5px;
      }
    }
  }

  .header {
    background-image: url("~/assets/images/aboutus/map-bg.png");
    background-position: center 16px;
    background-repeat: no-repeat;
  }
}

section.header-nav {
  width: 1204px;
  margin: 0 auto;
  padding-top: 47px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;

  figure.logo {
    flex: 0 0 auto;

    img {
      width: 160px;
    }
  }

  .page-title {
    flex: 1 1 auto;
    padding-left: 24px;

    h1 {
      font-size: 20px;
      font-weight: bold;

      &::before {
        content: "|";
        display: inline-block;
        color: #BFBFBF;
        margin-right: 24px;
      }

    }
  }

  .extra {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #000000;

    .language-selector {
      margin-left: 50px;

      .text {
        font-size: 16px;
      }
    }
  }
}

section.company-profile {
  width: 1204px;
  margin: 0 auto;
  padding-top: 100px;

  .profile-content {
    align-items: center;
    margin-top: 72px;
    padding: 0 12px;
    margin-bottom: 40px;

    .figure {
      width: 1204px;
      height: 350px;
      margin-bottom: 20px;
      img {
        width: 580px;
        height: 310px;
        float: left;
        margin: 0 40px 25px 0;
        border-radius: 15px;
      }
      &::after {
        content: '';
        clear: both;
      }
    }

    .figure.right {
      img {
        float: right;
        margin: 0 0 25px 40px;
      }
      &::after {
        content: '';
        clear: both;
      }
    }

    h3 {
      font-size: 24px;
      color: #333;
      margin-bottom: 10px;
    }
    h4 {
      color: #333;
      font-size: 20px;
      margin: 0 0 25px;
    }
    p {
      color: #333;
      margin: 0;
      font-size: 18px;
      line-height: 26px;
      letter-spacing: 1px;
      margin: 0 0 25px;
    }
  }
}

section.profession {
  background-color: #F7F9FA;

  .profession-list {
    width: 1204px;
    margin: 0 auto;
    height: 399px;
    display: flex;
    padding-top: 40px;
    justify-content: space-between;


    .profession-item {
      width: 360px;
      box-sizing: border-box;

      figure {
        img {
          width: 64px;
          margin: 0 auto;
        }
      }

      .content {
        margin-top: 33px;
        
        .title {
          font-size: 29px;
          color: #333333;
          line-height: 35px;
          text-align: center;
        }

        .desc {
          font-size: 20px;
          color: #333333;
          line-height: 28px;
          margin-top: 17px;
        }
      }
    }
  }
}

section.global-office {
  width: 1204px;
  margin: 0 auto;
  padding: 156px 0 200px 0;

  .office-list {
    margin-top: 60px;
    display: flex;
    justify-content: space-between;

    &:first-child{
      margin-top: 70px;
    }

    .office-item {
      width: 382px;
      box-sizing: border-box;

      figure {
        img {
          width: 100%;
          display: block;
          height: 246px;
          border-radius: 20px;
        }
      }

      .content {
        margin-top: 32px;

        .title {
          text-align: center;
          font-size: 26px;
          color: #333333;
          line-height: 32px;
          margin-bottom: 16px;
        }

        .location, .contact{
          font-size: 18px;
          color: #333333;
          line-height: 29px;
        }
      }
    }
  }
}
</style>