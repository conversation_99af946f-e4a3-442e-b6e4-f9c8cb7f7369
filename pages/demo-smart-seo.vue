<template>
  <div class="demo-page">
    <h1>智能SEO演示页面</h1>
    
    <div class="language-info">
      <h2>当前语言信息</h2>
      <p><strong>检测到的语言:</strong> {{ currentLanguage }}</p>
      <p><strong>语言配置:</strong> {{ JSON.stringify(languageConfig, null, 2) }}</p>
      <p><strong>当前路径:</strong> {{ $route.path }}</p>
    </div>

    <div class="seo-info">
      <h2>动态SEO信息</h2>
      <div v-if="seoContent">
        <p><strong>标题:</strong> {{ seoContent.title }}</p>
        <p><strong>描述:</strong> {{ seoContent.description }}</p>
        <p><strong>关键词:</strong> {{ seoContent.keywords }}</p>
      </div>
    </div>

    <div class="language-switcher">
      <h2>语言切换</h2>
      <button 
        v-for="lang in supportedLanguages" 
        :key="lang.key"
        @click="switchLanguage(lang.key)"
        :class="{ active: lang.key === currentLanguage }"
      >
        {{ lang.name }} ({{ lang.code }})
      </button>
    </div>

    <div class="browser-info">
      <h2>浏览器语言信息</h2>
      <p><strong>navigator.language:</strong> {{ browserLanguage }}</p>
      <p><strong>navigator.languages:</strong> {{ browserLanguages.join(', ') }}</p>
      <p><strong>Accept-Language (服务端):</strong> {{ acceptLanguage || '未检测到' }}</p>
    </div>
  </div>
</template>

<script setup>
// 使用智能SEO功能
const { generateSmartSeo } = useSeoOptimization()
const { 
  getCurrentLanguage, 
  getLanguageConfig, 
  getSupportedLanguages,
  generateLanguageUrl 
} = useLanguageDetection()

// 获取当前语言信息
const currentLanguage = ref(getCurrentLanguage())
const languageConfig = ref(getLanguageConfig())
const supportedLanguages = ref(getSupportedLanguages())

// 生成智能SEO
const seoContent = ref(null)
const route = useRoute()

// 在客户端更新语言信息
onMounted(() => {
  currentLanguage.value = getCurrentLanguage()
  languageConfig.value = getLanguageConfig()
  
  // 生成SEO内容示例
  const smartSeo = generateSmartSeo({
    path: route.path,
    title: '智能SEO演示页面',
    description: '这是一个演示动态SEO和语言检测功能的页面'
  })
  
  seoContent.value = {
    title: smartSeo.title,
    description: smartSeo.meta.find(m => m.name === 'description')?.content,
    keywords: smartSeo.meta.find(m => m.name === 'keywords')?.content
  }
})

// 浏览器语言信息
const browserLanguage = ref('')
const browserLanguages = ref([])
const acceptLanguage = ref('')

if (process.client) {
  browserLanguage.value = navigator.language
  browserLanguages.value = navigator.languages || []
}

// 从服务端获取Accept-Language信息
if (process.server) {
  const event = useRequestEvent()
  if (event) {
    acceptLanguage.value = event.node.req.headers['accept-language'] || ''
  }
}

// 语言切换功能
const switchLanguage = (lang) => {
  const targetUrl = generateLanguageUrl(lang, route.path)
  navigateTo(targetUrl)
}

// 设置页面SEO
useHead(generateSmartSeo({
  path: route.path,
  title: '智能SEO演示 - SmartDeer',
  description: '演示SmartDeer智能SEO和多语言检测功能的页面，支持根据用户浏览器语言自动适配SEO内容。'
}))
</script>

<style scoped>
.demo-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.language-info,
.seo-info,
.language-switcher,
.browser-info {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.language-switcher button {
  margin: 5px;
  padding: 10px 15px;
  border: 1px solid #007bff;
  background-color: white;
  color: #007bff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.language-switcher button:hover {
  background-color: #007bff;
  color: white;
}

.language-switcher button.active {
  background-color: #007bff;
  color: white;
  font-weight: bold;
}

h1, h2 {
  color: #333;
}

pre {
  background-color: #f4f4f4;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
