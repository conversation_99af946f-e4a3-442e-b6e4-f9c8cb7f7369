<template lang="pug">
.contries-page
  site-header(lang="en" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
    .countries-content
      h1.article-title SmartDeer Invited to Join, ‘Alibaba Cloud Go Global Alliance Ceremony’ Officially Established
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="en" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="en" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: 'SmartDeer Invited to Join, ‘Alibaba Cloud Go Global Alliance Ceremony’ Officially Established',
  ogDescription: '    On May 15, 2024, 2024 Alibaba C […]',
  ogSiteName: 'SmartDeer',
  description: '    On May 15, 2024, 2024 Alibaba C […]'
})
useHead({
  htmlAttrs: {
    lang: 'en-US'
  },
  title: 'SmartDeer Invited to Join, ‘Alibaba Cloud Go Global Alliance Ceremony’ Officially Established'
})
const status = reactive({
  showForm: false
})
const langSimple = 'en';
const pageTitle = 'SmartDeer Invited to Join, ‘Alibaba Cloud Go Global Alliance Ceremony’ Officially Established';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/08/ba9282858940724e6d1eea34be06994.png';
const flagImage = '${COUNTRY_FLAT_IMAGE_URL}';
const htmlContent = '<p>&nbsp;&nbsp;&nbsp;&nbsp;On May 15, 2024, 2024 Alibaba Cloud Fintech Go Global Summit, an event leading the globalization of Fintech, was grandly opened in Shanghai. At the summit, &nbsp;Deloitte, Alibaba Cloud and SmartDeer and other companies formed the “Alibaba&nbsp;Cloud&nbsp;Go&nbsp;Global&nbsp;Alliance&nbsp;Ceremony’ was officially established and launched. As the important member of the ceremony, SmartDeer was invited to attend the summit, and actively help Alibaba Cloud build new Eco-plate of Fintech Go Global, injecting new vitality into the globalization process of the industry.</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="1090" height="726" src="https://blog.smartdeer.work/wp-content/uploads/2024/08/阿里云3.png" alt="SmartDeer joining the Alibaba Cloud Go Global Alliance" class="wp-image-1475"/><figcaption class="wp-element-caption"><mark style="background-color:rgba(0, 0, 0, 0)" class="has-inline-color has-accent-3-color">SmartDeer‘s staff told the participant that SmartDeer is committed to providing global HR one-stop service for Chinese enterprises. </mark></figcaption></figure><figure class="wp-block-pullquote has-medium-font-size" style="padding-top:var(--wp--preset--spacing--10);padding-bottom:var(--wp--preset--spacing--10)"><blockquote><p>‘We help enterprises&nbsp;recruit the best global talent, handle global employment compliance and payroll issues, and provide a professional HR one-stop service.’</p><cite>SmartDeer&#8217;s relevant director said</cite></blockquote></figure>';
function submitSuccess() {
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>