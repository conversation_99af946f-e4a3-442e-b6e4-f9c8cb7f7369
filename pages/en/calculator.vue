<template>
  <div class="contries-page">
    <header>
      <site-header lang="en" source="countries" :showContactUs="false" @contact-us="() => { status.showForm = true; }" />
      <div class="header-banner">
        <div class="header-banner-text">
          <h1 class="header-title">Determine employee costs across the globe</h1>
          <p class="header-desc">
            Ready to hire in another country? Instantly aggregate employment costs based on team member locations to make the best hiring move for your business.
          </p>
        </div>
        <div class="header-form">
          <h2 class="calculator-title">Employee Cost Calculator</h2>
          <p class="calculator-desc">
            Select the country/region you wish to hire, and enter the employee's annual salary to check the employer cost in that location.
          </p>
          <div class="form-body">
            <el-form
              :model="form"
              ref="formRef"
              @change="handleFormChange"
            >
              <el-form-item
                label=""
                prop="country"
                :rules="[
                  { required: true, message: 'Please select the country/region' },
                ]"
                ><el-select
                  size="large"
                  v-model="form.country"
                  placeholder="Country/Region"
                  @change="onCountryChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in countryList"
                    :key="item.areaCode"
                    :label="item.areaNameI18n"
                    :value="item.areaCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="provinceList.length"
                label=""
                prop="province"
                :rules="[
                  { required: true, message: 'Please select the province/state' },
                ]"
                ><el-select
                  size="large"
                  v-model="form.province"
                  placeholder="Province/State"
                  @change="handleFormChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in provinceList"
                    :key="item.key"
                    :label="item.areaNameI18n"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="cityList.length"
                label=""
                prop="city"
                :rules="[{ required: true, message: 'Please select the city' }]"
              >
                <el-select
                  filterable
                  size="large"
                  v-model="form.city"
                  placeholder="City"
                  @change="handleFormChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in cityList"
                    :key="item.key"
                    :label="item.areaNameI18n"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="employTypeList.length"
                label=""
                prop="employType"
                :rules="[{ required: true, message: 'Please select employee type' }]"
                ><el-select
                  size="large"
                  v-model="form.employType"
                  placeholder="Employee Type"
                  @change="handleFormChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in employTypeList"
                    :key="item.key"
                    :label="item.sectionNameI18n"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
              <el-form-item v-if="form.currency" label="" prop="currency">
                <el-input
                  size="large"
                  v-model="form.currency"
                  disabled
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item
                label=""
                prop="annualSalary"
                :rules="[
                  { required: true, message: 'Please input the annual salary' },
                  { type: 'number', message: 'Please enter a valid number.' },
                ]"
                ><el-input
                  size="large"
                  v-model.number="form.annualSalary"
                  placeholder="Gross annual salary"
                  @input="handleFormChange"
                  style="width: 100%"
                />
              </el-form-item>
              <div class="errorMessage"  @click="status.showForm = true">
                {{ errorMessage }}
              </div>

              <el-button :disabled="errorMessage !== '' || !form.country || !form.annualSalary || (cityList.length > 0 && !form.city)" class="calculator-submit" @click="fetchEmployCost"
                >Get Employer Cost</el-button
              >
            </el-form>
          </div>
        </div>
      </div>
    </header>
    <div id="calculate-result" class="calculator-container">
      <h2 class="result-title">Determine employee costs across the globe</h2>
      <p class="result-description">
        SmartDeer can help you decide the best place to hire your next employee and get a comprehensive understanding of the taxes, fees, benefits, and more you can expect to pay.
      </p>
      

      <div class="periodChange" v-if="status.showTable">
        <client-only>
          <el-dropdown @command="handlePeriodChange">
            <div class="el-dropdown-time" @click.prevent>
              <img width="12" src="~/assets/images/calculator/icon-choose-time.png" alt="Time selection icon">
              {{ period === 'year' ? 'year' : 'month' }}
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="year">year</el-dropdown-item>
                <el-dropdown-item command="month">month</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </client-only>
      </div>


      <table v-if="status.showTable" class="result-table">
        <tr>
          <td class="cost-name">Gross Annual Salary {{ period === 'year' ? 'Total' : 'Monthly' }}</td>
          <td class="cost-amount">
            {{ form?.currency }}
            {{ period === 'year' ? formatNumber(calculateRes?.salary) : formatNumber(calculateRes?.salaryMonthly) }}
          </td>
        </tr>
        <tr>
          <td class="cost-name cost-position">
            <span class="icon">
              <ArrowDown
                v-if="!status.showTableDetails"
                @click="() => { status.showTableDetails = true; }"
              />
              <ArrowUp
                v-if="status.showTableDetails"
                @click="() => { status.showTableDetails = false; }"
              />
            </span>
            <span>Total {{ period === 'year' ? 'Annual' : 'Monthly' }} Employer Costs</span>
          </td>
          <td class="cost-amount">
            {{ form?.currency }}
            {{ period === 'year' ? formatNumber(calculateRes.netTotalCosts) : formatNumber(calculateRes.netTotalCostsMonthly) }}
          </td>
        </tr>
        <template v-if="status.showTableDetails">
          <tr
            v-for="(cost, cost_index) in period === 'year' ? calculateRes?.costs : calculateRes?.costsMonthly"
            :key="cost_index"
          >
            <td class="cost-name-detail">{{ cost?.sectionNameI18n }}</td>
            <td class="cost-amount-detail">
              {{ form?.currency }} {{ formatNumber(cost?.costOrigin) }}
            </td>
          </tr>
        </template>
        <tr>
          <td class="cost-name">Total {{ period === 'year' ? 'annual' : 'Monthly' }} cost</td>
          <td class="cost-amount">
            {{ form?.currency }}
            {{ period === 'year' ? formatNumber(calculateRes?.totalCosts) : formatNumber(calculateRes?.totalCostsMonthly) }}
          </td>
        </tr>
      </table>
      <p v-if="status.showTable" class="result-notice">
        Calculations are estimates based on a country's local tax and compliance costs, and net payments and employer contributions may vary based on individual employee data.
      </p>
      <p v-if="status.showTable" class="result-notice " v-for="item in descriptionI18n" :key="item">
        {{ item }}
      </p>
      <p class="entity-notice">
        SmartDeer has entities worldwide. This means you can hire employees in all of these countries without opening your own corporate entity.
      </p>
      <button class="calculator-contact" @click="handleContactUs">
        Contact Us
      </button>
    </div>

    <site-footer
      lang="en"
      @contact-us="() => { status.showForm = true; }"
    />

    <el-dialog v-model="status.showForm" title="" :width="600">
      <client-only>
        <contact-us-form @submit="submitSuccess" lang="en" />
      </client-only>
    </el-dialog>
    
  </div>

</template>

<script lang="ts" setup>
import { ElDialog, ElForm, ElFormItem, ElButton, ElInput, ElSelect, ElOption, ElMessage, ElDropdown, ElDropdownMenu, ElDropdownItem, ElCarousel, ElCarouselItem, ElNotification } from 'element-plus'
import langTool from '~/assets/utils/lang'
import {getQueryString} from "assets/utils";
import { reactive, ref, onMounted, onBeforeUnmount } from "vue";
import costConfig from '~/assets/utils/global-cost-config';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'



definePageMeta({ layout: 'basic' })
const emit = defineEmits(['contactUs'])
useHead({
  htmlAttrs: { lang: 'en' },
  title: 'Global Employee Cost Calculator - Free Online Tool | SmartDeer HR Solutions',
  meta: [
    // Basic SEO
    { name: 'description', content: 'Use SmartDeer\'s free global employee cost calculator to instantly calculate employer costs, taxes, and benefits across 150+ countries. Get accurate budget planning for international recruitment and make informed hiring decisions.' },
    { name: 'keywords', content: 'employee cost calculator, global payroll calculator, international recruitment costs, employer cost calculation, global hiring calculator, international salary calculator, EOR cost calculator, overseas employment costs' },
    { name: 'author', content: 'SmartDeer' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },

    // Canonical and hreflang
    { name: 'canonical', content: 'https://smartdeer.work/en/calculator' },

    // Open Graph
    { property: 'og:title', content: 'Global Employee Cost Calculator - SmartDeer HR Solutions' },
    { property: 'og:description', content: 'Calculate global employee costs instantly with SmartDeer\'s free calculator. Get accurate employer costs, taxes, benefits for 150+ countries. Plan your international hiring budget.' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'https://smartdeer.work/en/calculator' },
    { property: 'og:image', content: 'https://smartdeer.work/images/calculator/cost-calculator-preview.png' },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:locale', content: 'en_US' },

    // Twitter Card
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'Global Employee Cost Calculator - Free Tool' },
    { name: 'twitter:description', content: 'Calculate global employee costs instantly. Get accurate employer costs, taxes, benefits for 150+ countries with SmartDeer\'s free calculator.' },
    { name: 'twitter:image', content: 'https://smartdeer.work/images/calculator/cost-calculator-preview.png' },
    { name: 'twitter:image:alt', content: 'SmartDeer Global Employee Cost Calculator Interface' }
  ],
  link: [
    // Canonical URL
    { rel: 'canonical', href: 'https://smartdeer.work/en/calculator' },

    // Hreflang for multilingual SEO
    { rel: 'alternate', hreflang: 'en', href: 'https://smartdeer.work/en/calculator' },
    { rel: 'alternate', hreflang: 'zh-CN', href: 'https://smartdeer.work/zh/calculator' },
    { rel: 'alternate', hreflang: 'ja-JP', href: 'https://smartdeer.work/ja/calculator' },
    { rel: 'alternate', hreflang: 'x-default', href: 'https://smartdeer.work/en/calculator' }
  ],
  script: [
    // Structured Data - WebApplication
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Global Employee Cost Calculator",
        "description": "Free online calculator to determine employee costs across the globe including taxes, benefits, and employer contributions",
        "url": "https://smartdeer.work/en/calculator",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web Browser",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD"
        },
        "provider": {
          "@type": "Organization",
          "name": "SmartDeer",
          "url": "https://smartdeer.work"
        },
        "featureList": [
          "Calculate employer costs for 150+ countries",
          "Include taxes and mandatory benefits",
          "Support multiple currencies",
          "Instant cost breakdown",
          "Annual and monthly calculations"
        ],
        "screenshot": "https://smartdeer.work/images/calculator/calculator-screenshot.png"
      })
    }
  ]
})

const scrollItems = []

const status = reactive({
  showForm: false,
  showConsultantCode: false,
  showTable: false,
  showTableDetails: true
})

const formRef = ref()

const countryList = ref([])
const provinceList = ref([])
const allCityList = ref([]);
const cityList = ref([]);
const employTypeList = ref([])
const descriptionI18n = ref([]);
const errorMessage = ref('');

const form = reactive({
  country: '',
  province: '',
  city: '',
  currency: '',
  employType: '',
  annualSalary: null
})

const calculateRes = ref({})

const period = ref('year');
function handlePeriodChange(cmd) {
  period.value = cmd;
}

function handleFormChange() {
  cityList.value = allCityList.value.filter(item => item.province === form.province);
  // fetchEmployCost();
}

function handleContactUs() {
  status.showForm = true
}

function formatNumber(number) {
  const formatter = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
  return formatter.format(number)
}

const host = "https://www-api.smartdeer.work/v1/website/function/runtime";
const hostTest = "https://www-api-test.smartdeer.work/v1/website/function/runtime";
const fetchCountry = async () => {
  const res = await fetch(host, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      'accept-language': 'en-US'
    },
    body: JSON.stringify({
      functionKey: "search_website_smd_employer_cost_country_conf",
      params: {
        current: 1,
        size: 100,
        limit: 100,
        searchInfo: {
          searchItems: [],
          orders: [
            {
              key: "displayOrder",
              asc: "ASC",
            },
          ],
        },
      },
    }),
  });
  const {
    data: { dataInfo },
  } = await res.json();
  countryList.value = dataInfo || [];
};

const fetchCurrency = async (countryCode) => {
  errorMessage.value = '';
  const res = await fetch(`${host}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      'accept-language': 'en-US'
    },
    body: JSON.stringify({
      functionKey: "x_website_employer_cost_country_conf",
      params: {
        countryCode,
      },
    }),
  });
  const { data } = await res.json();
  if (data?.enableConf?.calculateSupport === 'false') {
    errorMessage.value = data?.enableConf?.descriptionI18n || 'The current country does not support cost calculation.';
    return;
  }
  provinceList.value = data?.provinceConf || [];
  allCityList.value = data?.cityConf || [];
  employTypeList.value = data?.employTypeConf || [];
  form.currency = data?.currencyConf || "";
  descriptionI18n.value = data?.descriptionConf?.descriptionI18n?.split('\n') || [];
};

const fetchEmployCost = async () => {
  if (!form.country || !form.annualSalary) return;
  const params: any = {
    countryCode: form.country,
    employeeSalary: form.annualSalary,
    ...(provinceList.value.length && form.province
      ? { provinceCode: form.province }
      : {}),
    ...(employTypeList.value.length && form.employType
      ? { employType: form.employType }
      : {}),
    ...(cityList.value.length && form.city
      ? { cityCode: form.city }
      : {}),
  };
  const res = await fetch(host, {
    method: "POST",
    headers: { 
      "Content-Type": "application/json",
      'accept-language': 'en-US'
    },
    body: JSON.stringify({
      functionKey: "x_website_cal_employer_cost",
      params,
    }),
  });
  const { data } = await res.json();
  calculateRes.value = {};
  calculateRes.value = data;
  status.showTable = true;
  scrollTo("#calculate-result");
};

const onCountryChange = async (val) => {
  form.province = "";
  form.employType = "";
  form.currency = "";
  provinceList.value = [];
  allCityList.value = [];
  cityList.value = [];
  employTypeList.value = [];
  await fetchCurrency(val);
  handleFormChange();
};

onMounted(() => {
  window.addEventListener('scroll', scroll)

  const curScroll = getQueryString('scroll')
  if (curScroll) {
    scrollTo('#' + curScroll)
  }
  fetchCountry();
})

onBeforeUnmount(() => {
  window.removeEventListener('scroll', scroll)
})

function scrollTo(tag) {
  const ele = window.document.querySelector(tag)
  if (ele) window.scrollTo({
    top: ele.offsetTop,
    behavior: 'smooth'
  })
}

// 自定义指令
const vScrollShow = {
  mounted: (el, bindings) => {
    const delayOffset = bindings.value.delayOffset || 0
    scrollItems.push({ offsetTop: el.offsetTop + delayOffset, el: el })
  }
}

let timer = null
function scroll(e) {
  if (timer) return
  timer = setTimeout(() => {
    const offset = window.scrollY + window.innerHeight
    scrollItems.forEach((item, index) => {
      if (item.offsetTop < offset) {
        item.el.setAttribute('show', true)
        scrollItems.splice(index, 1)
      }
    })
    timer = null
  }, 30)
}

function switchLang(lang) {
  langTool.swithLang(lang)
}

function submitSuccess() {
  status.showForm = false
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
}
</script>

<style lang="scss" scoped>
.errorMessage {
  color: red;
  font-size: 14px;
  margin: 20px 0;
  cursor: pointer;
}
header {
  background: #fff6ec;
  .header-banner {
    position: relative;
    width: 1204px;
    height: 620px;
    margin: 0 auto;
    background-image: url("~/assets/images/calculator/banner.png");
    background-size: 1204px 590px;
    background-position: 0px;
    background-repeat: no-repeat;
  }

  .header-banner-text {
    position: absolute;
    left: 0;
    width: 641px;
    .header-title {
      font-size: 40px;
      font-weight: bold;
      margin-top: 190px;
    }
    .header-desc {
      font-size: 18px;
      line-height: 30px;
      letter-spacing: 1px;
      font-weight: 300;
    }
  }
  .header-form {
    position: absolute;
    top: 80px;
    right: 0;
    width: 405px;
    background: #FFF;
    border: 1px #EFF0F6 solid;
    border-radius: 22px;
    box-sizing: border-box;
    box-shadow: 0 10px 14px 0 rgba(74, 58, 255, 0.01),0 9px 26px 0 rgba(23, 15, 73, 0.05);
    padding: 40px 30px;

    .calculator-title {
      color: #170F49;
      text-align: center;
    }
    .calculator-desc {
      color: #6F6C90;
    }

    .calculator-submit {
      color: #FFF;
      width: 100%;
      height: 56px;
      line-height: 56px;
      border-radius: 50px;
      background: linear-gradient(259deg, #F54A25 -42%, #FFAB71 98%);
    }
  }
}
.el-dropdown-time{
  display: flex;
  gap: 10px;
  .el-icon--right{
    width: 10px;
    height: 10px;
  }
}
.periodChange {
  display: flex;
  justify-content: flex-end;
  cursor: pointer;
}
.calculator-container {
  width: 1204px;
  margin: 0 auto;

  .result-title {
    margin-top: 50px;
    font-size: 36px;
    text-align: center;
  }

  .result-description {
    font-size: 18px;
    text-align: center;
    color: #6F6C90;
    line-height: 30px;
  }

  .result-table {
    border-collapse: collapse;
    width: 100%;
    margin-top: 50px;
  }

  .result-table tr, .result-table th, .result-table td {
    border: 1px #DBDEE5 solid;
  }

  .result-table td {
    width: 50%;
    font-size: 14px;
    height: 28px;
    padding: 24px 30px;
    color: #170F49;
    font-weight: 500;
  }

  .cost-name-detail, .cost-amount-detail {
    background: rgba(239, 239, 239, 0.3);
  }

  .cost-position {
    position: relative;
  }

  .cost-position .icon {
    cursor: pointer;
    position: absolute;
    display: block;
    width: 30px;
    height: 30px;
    right: 18px;
    top: 24px;
  }

  .result-notice {
    color: #6F6C90;
    font-size: 12px;
    line-height: 16px;
    margin-top: 15px;
  }

  .entity-notice {
    margin-top: 68px;
    font-size: 32px;
    font-weight: 400;
    color: #170F49;
  }

  .calculator-contact {
    display: block;
    cursor: pointer;
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    border-radius: 50px;
    width: 290px;
    height: 72px;
    color: #FFF;
    border: 0;
    background: linear-gradient(252deg, #F54A25 -45%, #FFAB71 98%);
    box-shadow: 0 4px 8px 0 rgba(255, 129, 42, 0.29);
    margin: 20px auto 75px;
  }
}
</style>

<style>
.el-notification__content {
  text-align: left;
  color: #000;
  font-weight: bold;
}
</style>