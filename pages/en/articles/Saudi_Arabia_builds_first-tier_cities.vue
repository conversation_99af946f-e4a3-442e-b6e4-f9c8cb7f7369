<template lang="pug">
.contries-page
  site-header(lang="en" :showContactUs="false")

  .countries
    .countries-content
      h1.article-title 沙特打造一线城市：AI引领，未来已来
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="en" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="en" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '沙特打造一线城市：AI引领，未来已来',
  ogDescription: '沙特为什么要在沙漠建设170km长的直线城市呢？如果你想要出海沙特，那你一定要看透这其中的商机。这个城市被称为 […]',
  ogSiteName: 'SmartDeer',
  description: '沙特为什么要在沙漠建设170km长的直线城市呢？如果你想要出海沙特，那你一定要看透这其中的商机。这个城市被称为 […]'
})
useHead({
  htmlAttrs: {
    lang: 'en-US'
  },
  title: '沙特打造一线城市：AI引领，未来已来'
})
const status = reactive({
  showForm: false
})
const langSimple = 'en';
const pageTitle = '沙特打造一线城市：AI引领，未来已来';
const bannerImage = '';
const flagImage = '${COUNTRY_FLAT_IMAGE_URL}';
const htmlContent = '<p>沙特为什么要在沙漠建设170km长的直线城市呢？如果你想要出海沙特，那你一定要看透这其中的商机。这个城市被称为线。在摩天大楼的内部，将建设所有必要的城市基础设施，包括绿色公园、体育场等。居民将在一个工业聚集区就业，预计将提供大约30万人的工作岗位。这里没有汽车，也没有供汽车行驶的道路，只有比高铁速度还快的公共交通。平时只要出门步行五分钟就能到达所有基础设施，包括学校、医院、商场等。而且城市的能源全是风能、太阳能、氢能等可持续的清洁能源，可见沙特正在努力摆脱石油经济。沙特最喜欢世界之最，最高的楼，最高的酒店，越是科幻，越是难度大，越是能吸引更多的游客来消费旅游。未来几百万人口居住在一条必值的城市里，零碳排放，100%可再生能源，用人工智能来监控整个城市，这样的房子简直就是人类的梦幻生活，这一切对于企业来说，在能源、基础建设、配套设施上也充满了商机，如果你有意到沙特发展，但是担心没有海外员工，我们smartdeer可以帮助到你，我们给不少出海沙特的企业提供全球招聘团队、海外招聘平台、海外校园招聘，在帮助企业快速搭建本地化团队解决用工问题上，有非常丰富的经验，如果你有更好的技术手段可以解决沙特这些问题，他们肯定会诚心与你合作。</p>';
function submitSuccess() {
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>