<template lang="pug">
.contries-page
  site-header(lang="en" :showContactUs="false")

  .countries
    .countries-content
      h1.article-title SmartDeer Data Processing Agreement
      div(v-html="htmlContent" )
  site-footer(lang="en" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: 'SmartDeer Data Processing Agreement',
  ogDescription: 'SmartDeer Data Processing Agreement',
  ogSiteName: 'SmartDeer',
  description: 'SmartDeer Data Processing Agreement'
})
useHead({
  htmlAttrs: {
    lang: 'en-US'
  },
  title: 'SmartDeer Data Processing Agreement'
})
const status = reactive({
  showForm: false
})
const langSimple = 'en';
const pageTitle = 'SmartDeer Data Processing Agreement';
const bannerImage = '[object Object]';
const flagImage = '';
const htmlContent = '<p>Last Updated: July 10, 2025</p><p>This Data Processing Agreement (hereinafter referred to as &#8220;DPA&#8221;), forms part of the agreement between the customer and IDEAL-CAREERBRIDGE HOLDINGS (HK) LIMITED and its related entities (each a &#8220;SmartDeer Group member&#8221;, collectively the &#8220;SmartDeer Group&#8221;), as well as between the customer and its affiliates. Unless the customer has entered into a separate written agreement with the SmartDeer Group, this DPA constitutes part of such agreement (hereinafter collectively referred to as the &#8220;Agreement&#8221;).</p><p></p><p>The SmartDeer Group and the customer shall each be referred to as a &#8220;party&#8221;, and collectively as the &#8220;parties&#8221;. By accepting this DPA through the SmartDeer platform or upon first use of the SmartDeer Group services, the customer indicates its agreement to be bound by the terms hereof.</p><p>This DPA applies to the processing of personal data covered hereby by the customer and the SmartDeer Group in accordance with applicable data protection laws for the provision of services. As part of the contractual relationship, the parties undertake to comply with the relevant data protection laws applicable to the processing of personal data covered by this DPA.</p><p><strong>I. Definitions</strong></p><p>1. &#8220;Affiliate&#8221; means (1) an entity directly or indirectly controlled by IDEAL-CAREERBRIDGE HOLDINGS (HK) LIMITED, or under common control with it; or (2) an entity that operates in collaboration with the SmartDeer Group pursuant to a separate written agreement.</p><p>2. &#8220;Data Protection Laws&#8221; refers to any applicable laws, regulations, or other binding obligations (including all legislative and/or regulatory amendments or successor provisions thereto), which are updated from time to time, originating from China, the European Union, the European Economic Area, Switzerland, the United Kingdom, the United States, Canada, Australia, or any other jurisdiction, governing or otherwise applicable to the processing of personal data under this Agreement.</p><p>3. &#8220;Personal Data&#8221; includes &#8220;personal data&#8221;, &#8220;personal information&#8221;, &#8220;personal identity information&#8221;, and similar terms as defined in Data Protection Laws.</p><p>4. &#8220;Processing&#8221; means any operation or set of operations performed on personal data, whether or not by automated means, such as collection, recording, organization, structuring, storage, adaptation or alteration, retrieval, consultation, use, disclosure by transmission, dissemination or otherwise making available, alignment or combination, restriction, erasure, or destruction.</p><p>5. &#8220;Security Breach&#8221; refers to any accidental or unlawful access to, destruction of, loss of, alteration of, unauthorized disclosure of, or access to the customer&#8217;s personal data.</p><p>6. &#8220;Subprocessor&#8221; means any third party engaged by the SmartDeer Group to process the customer&#8217;s personal data.</p><p>7. &#8220;Supervisory Authority&#8221; means a public authority established in other applicable jurisdictions pursuant to Data Protection Laws.</p><p>8. &#8220;Standard Contractual Clauses&#8221; refers to any type of standardized contractual clauses approved by the relevant competent authority, such as in the case of the EU General Data Protection Regulation (GDPR), the contractual clauses annexed to the Implementing Decision (EU) 2021/914 of the European Commission of June 4, 2021, on standard contractual clauses for the transfer of personal data to third countries under Regulation (EU) 2016/679 of the European Parliament and of the Council (&#8220;EU Standard Contractual Clauses&#8221;). The Standard Contractual Clauses are incorporated into this DPA by reference and form an integral part hereof.</p><p><strong>II. Term of Agreement</strong></p><p>1. The term of this DPA shall correspond to the term of the Agreement, and the termination of the Agreement shall also lead to the termination of this DPA.</p><p>2. In addition, if a party breaches the statutory or contractual data protection obligations under Data Protection Laws, and the contracting party involved cannot reasonably be expected to continue performing this DPA, then this annex may be terminated early upon prior written notice to the other party.</p><p>3. The parties confirm that the termination of the DPA, for any reason at any time, shall not discharge them from their obligations under Data Protection Laws regarding the collection, processing, and use of personal data.</p><p><strong>III. Data Processing</strong></p><p>1. The SmartDeer Group shall act as the data controller, responsible for processing personal data for the purposes related to the services, specifically for the following purposes:</p><p>a) Customer Relationship Management: for managing relationships with customers.</p><p>b) Core Business Operations: to support the core business of the SmartDeer Group, including accounting and tax filing.</p><p>c) Security and Compliance: for detecting, preventing, or investigating security incidents, fraud, and other abuses or misuse of the services.</p><p>d) Authentication: for verifying user identities.</p><p>e) Legal Compliance: to comply with legal or regulatory obligations of the SmartDeer Group.</p><p>f) Other Permitted Processing: any other processing carried out within the scope permitted by Data Protection Laws, in accordance with this annex, the Agreement, and the SmartDeer Group&#8217;s privacy policy.</p><p>2. Where the SmartDeer Group acts as a data controller, the customer, as an independent data controller, shall each bear independent responsibilities in accordance with applicable Data Protection Laws, and the parties shall not be joint controllers. The SmartDeer Group shall process personal data in its capacity as a controller. When the SmartDeer Group acts in the capacity of a data processor, the customer shall be the controller, determining the purposes and means of data processing. The customer shall be fully responsible for any processing of personal data carried out during its use of the SmartDeer Group services for managing daily activities with advisors. To avoid doubt, the SmartDeer Group is neither a data controller nor a data processor in respect of data processing actions directly authorized by the customer through its advisors.</p><p>3. Applicable Law. Each party shall comply with all laws, rules, and regulations applicable to it and binding upon it in the performance of this DPA, including Data Protection Laws.</p><p>4. Processing Instructions. The SmartDeer Group shall process the customer&#8217;s personal data on behalf of and in accordance with the customer&#8217;s written instructions regarding the processing of the customer&#8217;s personal data by the SmartDeer Group, as specified below:</p><p>a) Processing in accordance with the Agreement and applicable orders;</p><p>b) Processing initiated by users when authorized by the customer to use the SmartDeer services; and</p><p>c) Processing to comply with other written reasonable instructions provided by the customer (e.g., via email), provided that such instructions are consistent with the terms of the Agreement.</p><p><strong>IV. SmartDeer Group Employees and Data Protection Officer</strong></p><p>1. SmartDeer Employees. SmartDeer shall ensure that employees involved in processing the customer&#8217;s personal data are aware of the confidential nature of such data, have received appropriate training regarding their obligations, and have signed written confidentiality agreements. SmartDeer shall also:</p><p>a) Take commercially reasonable measures to ensure the reliability of any SmartDeer employees involved in processing the customer&#8217;s personal data; and</p><p>b) Ensure that access by SmartDeer to the customer&#8217;s personal data is limited to employees performing the SmartDeer services under this Agreement.</p><p>2. Data Protection Officer. The SmartDeer Group has appointed data protection officers for certain jurisdictions. The designated personnel can be <NAME_EMAIL>.</p><p><strong>V. Data Transfer Clauses between Data Controllers</strong></p><p>With respect to the personal data processed by the parties as controllers under this annex, each party shall:</p><p>1. Assist the other party in fulfilling its obligations under Data Protection Laws;</p><p>2. Promptly comply with any lawful requests from the other party, including access to, copying of personal data, or correction, transfer, or deletion of personal data, provided that such requests are necessary for either party to fulfill its obligations under Data Protection Laws;</p><p>3. Treat personal data as confidential information under this Agreement, not &#8220;sell&#8221; or &#8220;share&#8221; personal data, or process personal data for the purposes of targeted advertising, and ensure that authorized personnel undertake appropriate confidentiality obligations;</p><p>4. Immediately notify the other party upon discovering any breach of this clause, and the breaching party shall take all necessary measures to remedy such breach;</p><p>5. Both parties may cooperate with third parties in relation to the services and agree to comply with applicable Data Protection Laws regarding third parties. Each party shall be liable for the acts and omissions of its respective third parties to the same extent as the liability of such party under the terms of this DPA, unless otherwise provided in the Agreement.</p><p>6. Upon receiving any complaints related to the processing of personal data or the compliance of either party with Data Protection Laws under this annex (whether from a data subject, a supervisory authority, or any other party), the party shall notify the other party without undue delay and provide reasonable cooperation, information, and assistance to the other party in relation to any such complaints, notifications, or communications.</p><p><strong>VI. Data Transfer Clauses between Data Controller and Data Processor</strong></p><p>With respect to the personal data processed by the SmartDeer Group on behalf of the customer under this annex, the processor shall:</p><p>1. Process personal data only in accordance with the customer&#8217;s written instructions, unless required by law to process otherwise (in which case, if permitted by law, the customer shall be notified of such requirement prior to processing);</p><p>2. Process personal data only to the extent necessary and in an appropriate manner to fulfill its obligations under the Agreement, including processing anonymized and aggregated data for analysis and development purposes;</p><p>3. Maintain records of its processing of personal data and ensure the accuracy of such records;</p><p>4. Treat personal data as confidential information under this Agreement, not &#8220;sell&#8221; or &#8220;share&#8221; personal data, or process personal data for the purposes of targeted advertising, and ensure that authorized personnel undertake appropriate confidentiality obligations;</p><p>5. Promptly comply with any lawful requests from the customer, including access to, copying, modification, transfer, or deletion of personal data, provided that such requests are necessary for the customer to fulfill its obligations under Data Protection Laws, including obligations arising from requests from data subjects;</p><p>6. Only transfer personal data outside the European Economic Area if all the following conditions are met:</p><p>a) The party has established specific approved data transfer safeguards (recognized under Data Protection Laws) for such transfer;</p><p>b) Data subjects retain enforceable rights and effective legal remedies after the transfer;</p><p>c) The party provides an adequate level of protection for the transferred personal data (including through an adequacy decision by the European Commission); and</p><p>d) The party complies with reasonable instructions regarding the transfer;</p><p>7. Upon becoming aware of any loss, destruction, damage, unauthorized processing, or inaccessibility of personal data processed under this DPA, including unauthorized or illegal access or disclosure (&#8220;personal data breach&#8221;), the processor shall notify the customer without undue delay within 48 hours;</p><p>8. In the event of a personal data breach, the processor shall immediately provide comprehensive cooperation and assistance to the customer and furnish all information regarding the personal data breach in the processor&#8217;s possession, including:</p><p>a) The likely causes and consequences of the personal data breach;</p><p>b) The categories of personal data and the approximate number of data subjects involved; and</p><p>c) The measures taken by the processor to mitigate any damage;</p><p>9. To the extent permitted by law, SmartDeer shall promptly notify the customer of any complaints or requests from data subjects relating to the processing of their personal data (each such request being a &#8220;data subject request&#8221;). The customer, on its own behalf and, when acting as a processor, on behalf of its controller, authorizes SmartDeer to respond to any data subject who submits a data subject request to SmartDeer, confirming that SmartDeer has forwarded the request to the customer. If the customer is unable to handle data subject requests during its use of the SmartDeer services, SmartDeer shall, at the customer&#8217;s request and to the extent permitted by law and where Data Protection Laws require a response to such data subject requests, use commercially reasonable efforts to assist the customer in responding to such data subject requests. To the extent permitted by law, the customer shall be responsible for any costs incurred by SmartDeer in providing such assistance.</p><p>10. The processor shall not disclose personal data to any third party unless required by the customer or as otherwise provided in the Agreement;</p><p>11. Provide reasonable assistance to the customer in complying with its obligations under Data Protection Laws regarding security, data breach notification, data protection impact assessments, and consultations with supervisory or regulatory authorities;</p><p>12. During normal business hours, provide the customer with all necessary information to enable the customer to monitor the processor&#8217;s compliance with Data Protection Laws and its performance of obligations under this annex at any time.</p><p>13. At the end of the processing period, the SmartDeer Group shall delete or return the personal data to the customer, and delete or destroy existing copies at that time. If return or destruction is not feasible or is restricted by law, regulations, or rules, the SmartDeer Group shall take measures to prevent further processing of such personal data (unless required by law, regulations, or rules to continue processing) and shall continue to properly protect any remaining personal data in its possession, custody, or control.</p><p><strong>VII. Subprocessors</strong></p><p>The customer acknowledges and agrees that SmartDeer may engage subprocessors to provide the SmartDeer services. SmartDeer shall enter into a written agreement with each subprocessor, which shall impose data protection obligations no less stringent than those in this DPA. The customer may inquire about the current list of subprocessors for the SmartDeer services by sending an <NAME_EMAIL>. SmartDeer shall be liable for the acts and omissions of its subprocessors to the same extent as if SmartDeer were directly performing the services of each subprocessor under the terms of this DPA, unless otherwise provided in the Agreement.</p><p><strong>VIII. Security Incident Management</strong></p><p>1. The SmartDeer Group shall implement appropriate technical and organizational measures to address relevant risks and ensure the security, confidentiality, and integrity of personal data processed under this DPA.</p><p>2. These technical and organizational measures shall be adjusted in accordance with the current state of the art and technological advancements. In this regard, the SmartDeer Group may implement appropriate alternative measures, provided that such measures do not offer a lower level of security than the measures specified in this Agreement.</p><p>3. Third-Party Certification and Audits. SmartDeer employs external auditors to verify the sufficiency of its security measures. Upon the customer&#8217;s written request within a reasonable period and subject to the confidentiality obligations set forth in the Agreement, the SmartDeer Group shall provide non-SmartDeer Group competitor customers (or independent third-party auditors of non-SmartDeer Group competitor customers) with copies of SmartDeer&#8217;s then-current third-party audits or certifications (if any).</p><p>4. Upon becoming aware of a security breach, SmartDeer shall notify the customer without undue delay. SmartDeer shall make reasonable efforts to determine the cause of such security breach and take measures that SmartDeer deems necessary and reasonable to remediate the cause of the security breach where such remediation is within SmartDeer&#8217;s reasonable control. The obligations in this Agreement do not apply to security breaches caused by the customer or its users.</p><p>5. The SmartDeer Group has an obligation to assist. To enable the customer to report security breaches to supervisory authorities or data subjects (where applicable), SmartDeer shall cooperate with the customer and provide assistance, including disclosing information regarding the security breach to the customer, taking into account the nature of the processing, the information in SmartDeer&#8217;s possession, and any restrictions on disclosing such information, such as confidentiality.</p><p><strong>IX. Return and Deletion of Customer Personal Data</strong></p><p>At the request of the customer upon or prior to the termination of the Agreement, the SmartDeer Group shall reasonably cooperate with the customer to facilitate the export of such customer personal data from the SmartDeer Group&#8217;s systems, after which any and all remaining customer personal data shall be deleted from the same systems, unless further retention is required or prohibited by law.</p><p><strong>X. Data Transfers</strong></p><p>The customer acknowledges that the SmartDeer services may involve cross-border transfers of customer personal data. If the SmartDeer Group engages in any cross-border processing of customer personal data or transfers any customer personal data to any country outside the country where such customer personal data is located, the SmartDeer Group shall comply with Data Protection Laws. To the extent required by Data Protection Laws, the SmartDeer Group shall ensure that prior to engaging in any subsequent cross-border transfer of customer personal data from one country to another, a lawful data transfer mechanism has been established, and where applicable, the specific terms of the relevant jurisdiction shall be complied with.</p><p><strong>XI. Contact and Communication</strong></p><p>The customer, as a contracting party to this Agreement, shall continue to be responsible for coordinating all contacts with the SmartDeer Group under this DPA and shall have the authority to act on behalf of its authorized affiliates in sending and receiving any communications under this DPA.</p><p><strong>XII. Rights of Authorized Affiliates</strong></p><p>1. If an authorized affiliate becomes a party to the DPA entered into with SmartDeer, then to the extent required by applicable Data Protection Laws, it shall be entitled to exercise the rights and seek remedies under this DPA, subject to the following provisions:</p><p>a) Unless applicable Data Protection Laws require the authorized affiliate to directly exercise its rights under this DPA against the SmartDeer Group or seek any remedy, the parties agree that (i) only the customer as a contracting party to the Agreement may exercise any such rights on behalf of the authorized affiliate or seek any such remedy, and (ii) the customer as a contracting party to the Agreement shall exercise any such rights under this DPA not separately for each authorized affiliate, but in an aggregated manner for itself and all its authorized affiliates.</p><p>b) The parties agree that when the customer as a contracting party to the Agreement conducts on-site audits related to the protection of customer personal data, it shall take all reasonable measures to minimize the impact on the SmartDeer Group and its subprocessors by consolidating multiple audit requests, to the extent reasonably feasible, into a single audit on behalf of itself and all its authorized affiliates.</p><p><strong>XIII. Limitation of Liability</strong></p><p>To the extent permitted by Data Protection Laws, the liability of each party and all its affiliates arising from or in connection with this DPA, as well as the liability between authorized affiliates and the SmartDeer Group under all DPAs, whether in contract, tort, or any other theory of liability, shall be governed by the limitation of liability provisions in the SmartDeer Group&#8217;s customer agreement. Any reference to a party&#8217;s liability in such provisions shall refer to the total liability of such party and all its affiliates under the Agreement and all DPAs. To avoid doubt, the total liability of SmartDeer and its affiliates to the customer and all its authorized affiliates for all claims arising from or in connection with the Agreement and all DPAs shall apply to all claims under the Agreement and all DPAs established thereunder, including claims of the customer and all its authorized affiliates, and shall not be construed as applying separately and individually to the customer and/or any authorized affiliate as a contracting party to any such DPA.</p>';
function submitSuccess() {
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>