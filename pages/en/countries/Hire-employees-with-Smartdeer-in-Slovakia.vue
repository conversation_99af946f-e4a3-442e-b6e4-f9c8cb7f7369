<template lang="pug">
.contries-page
  site-header(lang="en" :showContactUs="false")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title Employer Record in Slovakia
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="en" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="en" @contact-us="handleScrollToTop")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: 'Employer Record in Slovakia',
  ogDescription: 'Basic information Capital:Bratislava Time zone: GMT+2 L […]',
  ogSiteName: 'SmartDeer',
  description: 'Basic information Capital:Bratislava Time zone: GMT+2 L […]'
})
useHead({
  htmlAttrs: {
    lang: 'en-US'
  },
  title: 'Employer Record in Slovakia'
})
const status = reactive({
  showForm: false
})
const langSimple = 'en';
const pageTitle = 'Employer Record in Slovakia';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/06/斯洛伐克-FI.jpg';
const flagImage = '${COUNTRY_FLAT_IMAGE_URL}';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">Basic information</h2><p>Capital:Bratislava</p><p>Time zone: GMT+2</p><p>Languages: Slovak</p><p>Currency code:EUR</p><h2 class="wp-block-heading has-large-font-size">Human Resources at a Glance&nbsp;</h2><h2 class="wp-block-heading has-large-font-size">Contract Terms</h2><p>Contracts must be in Slovak and can be bilingual. They must be in writing and signed by both parties, and the employee and employer must keep a signed copy. A contract must include:</p><ul><li>Name</li><li>Start date</li><li>Length of the employment</li><li>Job description</li><li>Termination conditions</li></ul><h2 class="wp-block-heading has-large-font-size">Guidelines Regarding Probation Period/Trial Period</h2><p>Probation periods are not mandatory. The maximum probation period is 90 days, and 180 days for managerial positions.</p><h2 class="wp-block-heading has-large-font-size">Regulations and Rules Regarding Working Hours</h2><p>Standard working hours are 8 hours per day, 40 hours per week. The standard workweek is from Monday to Friday.</p><p>Overtime payment is not mandatory and may be included in the salary or granted as leave. Hours outside of standard work hours are considered overtime.</p><h2 class="wp-block-heading has-large-font-size">Minimum Wage Requirements</h2><p>In Slovakia the minimum wage ranges from EUR750 to EUR1330 per month. The monthly minimum wage changes according to the degree of work intensity and responsibility and there are 6 degrees of work.</p><h2 class="wp-block-heading has-large-font-size">Termination&nbsp;</h2><h2 class="wp-block-heading has-large-font-size">Grounds</h2><p>Terminations in Slovakia can be terminated by either party with or without cause providing written notice.</p><p>Compliant terminations include:</p><ul><li>Voluntarily by the employee</li><li>By mutual agreement</li><li>Unilaterally by the employer based on:<ul><li>Probation period</li><li>Objective grounds</li><li>Disciplinary dismissal</li><li>Performance due to unsuitability for the job</li></ul></li><li>By the expiration of the contract</li></ul><h2 class="wp-block-heading has-large-font-size">Notice Period</h2><p>Notice will depend on the length of employment and if the employee resigns or is dismissed.</p><ul><li>Termination by the employer:<ul><li>30 days for up to one year of employment</li><li>60 days for one to five years of employment</li><li>90 days for over five years of employment</li></ul></li></ul><ul><li>Termination by the employee:<ul><li>30 days for up to one year of employment</li><li>60 days for more than one year of employment</li></ul></li></ul><h2 class="wp-block-heading has-large-font-size">Payment &amp; leave&nbsp;Compensation&nbsp;&amp; Holidays&nbsp;</h2><h2 class="wp-block-heading has-large-font-size">SalaryPayment</h2><p>Remuneration is paid in arrears by the 15th day of the following month.</p><h2 class="wp-block-heading has-large-font-size">Payslip&nbsp;</h2><p>Payslips must be provided two days at the latest following the pay date, either in hard copy or electronically.</p><h2 class="wp-block-heading has-large-font-size">Annual Leave</h2><p>Both full-time and part-time employees are entitled to 20 working days of paid time off (PTO) a year. PTO and accrual rates vary depending on the age of the employee.</p><figure class="wp-block-table"><table><tbody><tr><td>Age</td><td>PTO days</td><td>Monthly accrual</td></tr><tr><td>Up to 32 years</td><td>20</td><td>1.66 days</td></tr><tr><td>Over 32 years</td><td>25</td><td>2.08 days</td></tr><tr><td>Employees of any age with children</td><td>25</td><td>2.08 days</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">Sick Leave</h2><p>Employees are entitled to paid sick leave for up to 52 weeks. This leave is paid at different rates and by different payers depending upon length:</p><figure class="wp-block-table"><table><tbody><tr><td>Period</td><td>Pay</td><td>Payer</td></tr><tr><td>1 &#8211; 3 days</td><td>25% of base salary</td><td>Employer</td></tr><tr><td>4 &#8211; 10 days</td><td>55% of base salary</td><td>Employer</td></tr><tr><td>11 days+</td><td>55% of base salary</td><td>Social Security</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">Maternity &amp; Parental Leave</h2><p>Pregnant employees who have worked for 60 consecutive days with the same employer are entitled to 34 weeks of paid leave. Six to eight weeks must be taken before the child&#8217;s birth.</p><p>Social Security pays 75% of the daily measurement base for this time. The daily measurement base is calculated as: sum of the gross wages from the previous year divided by 12 months divided by 365 days multiplied by the number of days of maternity leave. The maximum daily payment for maternity leave is €55.87.</p><p>The employee can’t extend leave.</p><p>Employees are entitled to up to 3 years of parental leave until the child reaches the age of three. Either parent can take parental leave. Social Security will pay EUR 275.90 per month during this period.</p><p>Employees can’t extend parental leave.</p><h2 class="wp-block-heading has-large-font-size">Tax and Social Security Information</h2><h2 class="wp-block-heading has-large-font-size">Personal Income Tax</h2><p>The individual income tax ranges from 19% to 25%. Income tax is calculated according to gross annual income.</p><figure class="wp-block-table"><table><tbody><tr><td>Gross Annual Income</td><td>Tax Rate (%)</td></tr><tr><td>Up to EUR 37,163.36</td><td>19%</td></tr><tr><td>Over EUR 37,163.36</td><td>25%</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">Social Security</h2><p>Both the employer and employee must pay social security contributions.</p><figure class="wp-block-table"><table><tbody><tr><td>Payment</td><td>&nbsp;Employee&nbsp; Contribution (%)</td><td>&nbsp;&nbsp;Employer&nbsp; Contribution (%)</td><td>&nbsp;&nbsp;Maximum&nbsp;Assessment&nbsp;&nbsp; Base (EUR)</td></tr><tr><td>Sickness Insurance</td><td>&nbsp;1.4</td><td>&nbsp; 1.4</td><td>&nbsp; 8,477.00</td></tr><tr><td>Disability Insurance</td><td>&nbsp;3.0</td><td>&nbsp; 3.0</td><td>&nbsp; 8,477.00</td></tr><tr><td>Retirement Insurance</td><td>&nbsp;4.0</td><td>&nbsp; 14.0</td><td>&nbsp; 8,477.00</td></tr><tr><td>Unemployment Insurance</td><td>&nbsp;1.0</td><td>&nbsp; 1.0</td><td>&nbsp; 8,477.00</td></tr><tr><td>Work Injury Insurance</td><td>&nbsp;一</td><td>&nbsp; 0.8</td><td>&nbsp; No limit</td></tr><tr><td><br>Solidarity reserve fund (part of the retirement insurance)</td><td></td><td>&nbsp; 4.75</td><td><br>&nbsp; 8,477.00</td></tr><tr><td>Guarantee Fund</td><td>一</td><td>&nbsp; 0.25</td><td>&nbsp; 8,477.00</td></tr><tr><td>Health Insurance</td><td>&nbsp;4.0</td><td>&nbsp; 11.0</td><td>&nbsp; No limit</td></tr><tr><td>TOTAL</td><td>&nbsp;13.4</td><td>&nbsp; 36.2</td><td></td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">Public Holidays 2024</h2><figure class="wp-block-table"><table><tbody><tr><td>Occasion</td><td>Date</td></tr><tr><td>Republic Day</td><td>1.1</td></tr><tr><td>Epiphany</td><td>1.6</td></tr><tr><td>Good Friday</td><td>3.29</td></tr><tr><td>Easter Monday</td><td>4.1</td></tr><tr><td>Labour Day</td><td>5.1</td></tr><tr><td>Liberation Day</td><td>5.8</td></tr><tr><td>St.Cyril and Methodius Day</td><td>7.5</td></tr><tr><td>Slovak National Uprising Anniversary</td><td>8.29</td></tr><tr><td>Day of the Constitution of the Slovak Republic</td><td>9.1</td></tr><tr><td>Day of Our Lady of the Seven Sorrows</td><td>9.15</td></tr><tr><td>All Saints&#8217;Day</td><td>11.1</td></tr><tr><td>Freedom and Democracy Day</td><td>11.17</td></tr><tr><td>Christmas Eve</td><td>12.24</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr><tr><td>Second Day of Christmas</td><td>12.26</td></tr></tbody></table></figure><p></p><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>