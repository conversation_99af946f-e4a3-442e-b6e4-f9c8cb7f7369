<!--
 * @Author: xu.sun <EMAIL>
 * @Date: 2022-11-09 18:42:17
 * @LastEditors: xu.sun <EMAIL>
 * @LastEditTime: 2022-11-18 17:09:58
 * @FilePath: /bpo-website-pc/app.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template lang="pug">
div
  NuxtLayout
    NuxtPage
</template>

<script lang="ts" setup>
import { onMounted, computed } from 'vue'
import { ID_INJECTION_KEY } from 'element-plus'
import { switchingPlatforms } from '~/assets/utils'

const route = useRoute()
const siteConfig = useSiteConfig()

// Hreflang SEO Optimization
useHead(() => {
  const path = route.path
  const supportedLangs = ['en', 'zh', 'ja']
  const links = []

  // Function to switch language in the path
  const getLocalizedPath = (lang) => {
    const pathParts = path.split('/').filter(Boolean)
    if (supportedLangs.includes(pathParts[0])) {
      pathParts[0] = lang
    } else {
      pathParts.unshift(lang)
    }
    return `/${pathParts.join('/')}`
  }

  // Add hreflang links for each supported language
  supportedLangs.forEach(lang => {
    links.push({
      rel: 'alternate',
      hreflang: lang,
      href: `${siteConfig.url}${getLocalizedPath(lang)}`,
    })
  })

  // Add x-default hreflang link (pointing to English as a default)
  links.push({
    rel: 'alternate',
    hreflang: 'x-default',
    href: `${siteConfig.url}${getLocalizedPath('en')}`,
  })

  return {
    link: links,
  }
})


onMounted(() => {
  switchingPlatforms()
})

provide(ID_INJECTION_KEY, {
  prefix: 100,
  current: 0,
})
</script>

<style lang="scss">
@import '~/assets/styles/reset.scss';
</style>
