/**
 * 分析工具集成插件
 * 包含Google Analytics、Facebook Pixel、百度统计等
 */

export default defineNuxtPlugin(() => {
  // 只在客户端运行
  if (process.server) return

  const config = useRuntimeConfig()
  
  // Google Analytics 4 配置
  const initGoogleAnalytics = () => {
    const GA_ID = 'G-XXXXXXXXXX' // 替换为实际的GA4 ID
    
    // 加载gtag脚本
    const script = document.createElement('script')
    script.async = true
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_ID}`
    document.head.appendChild(script)
    
    // 初始化gtag
    window.dataLayer = window.dataLayer || []
    function gtag(...args: any[]) {
      window.dataLayer.push(args)
    }
    
    gtag('js', new Date())
    gtag('config', GA_ID, {
      page_title: document.title,
      page_location: window.location.href,
      send_page_view: true
    })
    
    // 将gtag函数暴露到全局
    window.gtag = gtag
  }
  
  // Facebook Pixel 配置
  const initFacebookPixel = () => {
    const FB_PIXEL_ID = 'XXXXXXXXXXXXXXXXX' // 替换为实际的Facebook Pixel ID
    
    // Facebook Pixel 代码
    !(function(f: any, b: any, e: any, v: any, n?: any, t?: any, s?: any) {
      if (f.fbq) return
      n = f.fbq = function() {
        n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments)
      }
      if (!f._fbq) f._fbq = n
      n.push = n
      n.loaded = !0
      n.version = '2.0'
      n.queue = []
      t = b.createElement(e)
      t.async = !0
      t.src = v
      s = b.getElementsByTagName(e)[0]
      s.parentNode.insertBefore(t, s)
    })(window, document, 'script', 'https://connect.facebook.net/en_US/fbevents.js')
    
    window.fbq('init', FB_PIXEL_ID)
    window.fbq('track', 'PageView')
  }
  
  // 百度统计配置
  const initBaiduAnalytics = () => {
    const BAIDU_ID = 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' // 替换为实际的百度统计ID
    
    const script = document.createElement('script')
    script.innerHTML = `
      var _hmt = _hmt || [];
      (function() {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?${BAIDU_ID}";
        var s = document.getElementsByTagName("script")[0]; 
        s.parentNode.insertBefore(hm, s);
      })();
    `
    document.head.appendChild(script)
  }
  
  // Microsoft Clarity 配置
  const initMicrosoftClarity = () => {
    const CLARITY_ID = 'xxxxxxxxxx' // 替换为实际的Clarity ID
    
    const script = document.createElement('script')
    script.innerHTML = `
      (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
      })(window, document, "clarity", "script", "${CLARITY_ID}");
    `
    document.head.appendChild(script)
  }
  
  // 初始化所有分析工具
  const initAnalytics = () => {
    try {
      // 延迟加载以提升性能
      setTimeout(() => {
        initGoogleAnalytics()
        initFacebookPixel()
        initBaiduAnalytics()
        initMicrosoftClarity()
        
        console.log('Analytics tools initialized successfully')
      }, 1000)
    } catch (error) {
      console.error('Failed to initialize analytics tools:', error)
    }
  }
  
  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initAnalytics)
  } else {
    initAnalytics()
  }
  
  // 提供全局事件跟踪函数
  const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
    try {
      // Google Analytics 事件跟踪
      if (window.gtag) {
        window.gtag('event', eventName, parameters)
      }
      
      // Facebook Pixel 事件跟踪
      if (window.fbq) {
        window.fbq('track', eventName, parameters)
      }
      
      console.log('Event tracked:', eventName, parameters)
    } catch (error) {
      console.error('Failed to track event:', error)
    }
  }
  
  // 将跟踪函数暴露到全局
  return {
    provide: {
      trackEvent
    }
  }
})

// 类型声明
declare global {
  interface Window {
    dataLayer: any[]
    gtag: (...args: any[]) => void
    fbq: (...args: any[]) => void
    _hmt: any[]
    clarity: (...args: any[]) => void
  }
}
