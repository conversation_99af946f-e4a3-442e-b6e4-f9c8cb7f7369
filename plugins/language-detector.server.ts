/**
 * 服务端语言检测插件
 * 在服务端渲染时检测用户语言偏好，为SEO优化提供正确的语言信息
 */

export default defineNuxtPlugin({
  name: 'language-detector-server',
  setup() {
    // 只在服务端运行
    if (process.client) return

    const { detectServerLanguage, getLanguageConfig } = useLanguageDetection()

    // 在服务端渲染时检测语言
    addRouteMiddleware('language-detection', (to) => {
      try {
        // 检测当前请求的语言偏好
        const detectedLanguage = detectServerLanguage()
        const languageConfig = getLanguageConfig(detectedLanguage)

        // 将语言信息添加到路由meta中，供SEO中间件使用
        to.meta = to.meta || {}
        to.meta.detectedLanguage = detectedLanguage
        to.meta.languageConfig = languageConfig

        // 设置全局语言状态
        const nuxtApp = useNuxtApp()
        nuxtApp.ssrContext = nuxtApp.ssrContext || {}
        nuxtApp.ssrContext.detectedLanguage = detectedLanguage
        nuxtApp.ssrContext.languageConfig = languageConfig

        console.log(`[服务端语言检测] 路径: ${to.path}, 检测语言: ${detectedLanguage}`)
      } catch (error) {
        console.error('[服务端语言检测] 检测失败:', error)
      }
    }, { global: true })
  }
})
