/*
 * @Author: xu.sun <EMAIL>
 * @Date: 2022-11-09 18:42:17
 * @LastEditors: sx <EMAIL>
 * @LastEditTime: 2023-01-16 17:50:45
 * @FilePath: /bpo-website-pc/nuxt.config.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// https://v3.nuxtjs.org/api/configuration/nuxt.config

// import AutoImport from 'unplugin-auto-import/vite'
// import Components from 'unplugin-vue-components/vite'
// import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

import ElementPlus from 'unplugin-element-plus/vite'

const env = process.env.BUILD_ENV || 'development'

const CONFIG = {
  test: {
    mobile_site_host: 'https://m-test.smartdeer.work',
    pc_site_host: 'https://smartdeer.com'
  },
  production: {
    mobile_site_host: 'https://m.smartdeer.work',
    pc_site_host: 'https://smartdeer.com'
  },
  development: {
    mobile_site_host: '',
    pc_site_host: 'https://smartdeer.com'
  }
}

export default defineNuxtConfig({
  // SEO 和性能优化配置
  experimental: {
    payloadExtraction: false // 提升性能
  },

  // 图片优化配置
  image: {
    quality: 80,
    format: ['webp', 'avif', 'jpeg'],
    screens: {
      xs: 320,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      xxl: 1536
    }
  },

  // 性能优化
  nitro: {
    compressPublicAssets: true,
    minify: true
  },

  // 全局 SEO 设置（将由动态语言检测覆盖）
  app: {
    head: {
      htmlAttrs: {
        lang: 'zh-CN'  // 默认语言，将被动态检测覆盖
      },
      title: 'SmartDeer - 中国企业出海HR服务 | 全球雇佣&EOR解决方案',
      titleTemplate: '%s | SmartDeer',
      meta: [
        // 基础 meta 标签
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' },
        { name: 'format-detection', content: 'telephone=no' },

        // SEO meta 标签（基于AI数据挖掘的高潜力关键词优化）
        { name: 'description', content: 'SmartDeer专注中国企业出海HR服务，提供EOR服务费用计算器、海外员工合规雇佣指南、跨境电商人才招聘。覆盖150+国家，助力企业安全合规出海。立即获取免费咨询！' },
        { name: 'keywords', content: '中国企业出海HR服务, EOR服务费用计算器, 海外员工合规雇佣指南, 跨境电商人才招聘, 一带一路国家雇佣服务, 远程工作全球合规管理, 科技公司海外扩张HR, 全球薪酬税务优化方案, SmartDeer' },
        { name: 'author', content: 'SmartDeer' },
        { name: 'robots', content: 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1' },

        // 百度 SEO 优化
        { name: 'baidu-site-verification', content: 'codeva-DuTIOCkSwLU370iL' },
        { name: 'applicable-device', content: 'pc' },
        { name: 'renderer', content: 'webkit' },

        // 必应 SEO 优化
        { name: 'msvalidate.01', content: '1C90A015812D60F281F96A31ACC73A2D' },
        { name: 'msapplication-TileColor', content: '#2d89ef' },

        // 性能和安全优化
        { 'http-equiv': 'X-UA-Compatible', content: 'IE=edge' },
        { name: 'theme-color', content: '#2d89ef' },
        { name: 'mobile-web-app-capable', content: 'yes' },
        { name: 'apple-mobile-web-app-capable', content: 'yes' },
        { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },

        // Open Graph 标签（基于高潜力关键词优化）
        { property: 'og:type', content: 'website' },
        { property: 'og:site_name', content: 'SmartDeer' },
        { property: 'og:title', content: 'SmartDeer - 中国企业出海HR服务 | 全球雇佣&EOR解决方案' },
        { property: 'og:description', content: 'SmartDeer专注中国企业出海HR服务，提供EOR服务费用计算器、海外员工合规雇佣指南、跨境电商人才招聘。覆盖150+国家，助力企业安全合规出海。立即获取免费咨询！' },
        { property: 'og:url', content: CONFIG[env].pc_site_host || 'https://smartdeer.com' },
        { property: 'og:image', content: 'https://www.smartdeer.work/images/tg_banner.png' },
        { property: 'og:image:width', content: '1390' },
        { property: 'og:image:height', content: '781' },
        { property: 'og:image:type', content: 'image/png' },
        { property: 'og:image:alt', content: 'SmartDeer - 全球人力资源服务平台' },
        { property: 'og:locale', content: 'zh_CN' },

        // Twitter Card 标签
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:site', content: '@smartdeer' },
        { name: 'twitter:creator', content: '@smartdeer' },
        { name: 'twitter:title', content: 'SmartDeer - 全球招聘雇佣，海外人力资源一站式服务平台' },
        { name: 'twitter:description', content: 'SmartDeer提供全球招聘、海外雇佣、薪酬管理等一站式人力资源服务，覆盖150+国家，助力企业全球化扩张。' },
        { name: 'twitter:image', content: 'https://www.smartdeer.work/images/tg_banner.png' },
        { name: 'twitter:image:alt', content: 'SmartDeer - 全球人力资源服务平台' }
      ],
      link: [
        // Favicon 和图标
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'canonical', href: CONFIG[env].pc_site_host || 'https://smartdeer.com' },

        // DNS 预解析
        { rel: 'dns-prefetch', href: '//hm.baidu.com' },
        { rel: 'dns-prefetch', href: '//www.googletagmanager.com' },
        { rel: 'dns-prefetch', href: '//bat.bing.com' },

        // 预连接重要资源
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },

        // 性能优化 - 预加载关键资源
        { rel: 'preload', href: '/images/tg_banner.png', as: 'image' },
        { rel: 'preload', href: '/favicon.ico', as: 'image' }
      ]
    },
    buildAssetsDir: '/static/'
  },
  runtimeConfig: {
    public: CONFIG[env]
  },
  build: {
    transpile: ['element-plus/es'],
  },
  vite: {
    plugins: [ElementPlus({ useSource: true })],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "~/assets/styles/element.scss" as *;`,
        },
      },
    },
  }
})
