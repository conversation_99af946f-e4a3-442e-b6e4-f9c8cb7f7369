/**
 * 语言检测和管理composable
 * 支持服务端和客户端语言检测，提供智能的语言选择和回退机制
 */

export interface LanguageConfig {
  code: string
  simple: string
  name: string
  region: string
}

export const useLanguageDetection = () => {
  const LANG_COOKIE_KEY = 'sd_intl_lang'
  
  // 支持的语言配置
  const supportedLanguages: Record<string, LanguageConfig> = {
    zh: {
      code: 'zh-CN',
      simple: 'zh',
      name: '中文',
      region: 'CN'
    },
    en: {
      code: 'en-US', 
      simple: 'en',
      name: 'English',
      region: 'US'
    },
    ja: {
      code: 'ja-JP',
      simple: 'ja', 
      name: '日本語',
      region: 'JP'
    }
  }

  // 默认语言（项目主要面向中文用户）
  const defaultLanguage = 'zh'

  /**
   * 从Accept-Language头部解析语言偏好
   */
  const parseAcceptLanguage = (acceptLanguage: string): string[] => {
    if (!acceptLanguage) return []
    
    return acceptLanguage
      .split(',')
      .map(lang => {
        const [language, quality = '1'] = lang.trim().split(';q=')
        return {
          language: language.toLowerCase(),
          quality: parseFloat(quality)
        }
      })
      .sort((a, b) => b.quality - a.quality)
      .map(item => item.language)
  }

  /**
   * 匹配最佳语言
   */
  const matchBestLanguage = (preferredLanguages: string[]): string => {
    for (const lang of preferredLanguages) {
      // 精确匹配
      if (supportedLanguages[lang]) {
        return lang
      }
      
      // 语言代码匹配（如 zh-CN -> zh）
      const langCode = lang.split('-')[0]
      if (supportedLanguages[langCode]) {
        return langCode
      }
      
      // 特殊处理中文变体
      if (lang.startsWith('zh')) {
        return 'zh'
      }
    }
    
    return defaultLanguage
  }

  /**
   * 服务端语言检测
   */
  const detectServerLanguage = (): string => {
    if (process.client) return defaultLanguage
    
    try {
      const event = useRequestEvent()
      if (!event) return defaultLanguage

      // 1. 检查URL路径中的语言
      const path = event.node.req.url || ''
      const pathLang = path.match(/^\/(zh|en|ja)\//)?.[1]
      if (pathLang && supportedLanguages[pathLang]) {
        return pathLang
      }

      // 2. 检查Cookie中的语言偏好
      const cookies = parseCookies(event.node.req.headers.cookie || '')
      const cookieLang = cookies[LANG_COOKIE_KEY]
      if (cookieLang && supportedLanguages[cookieLang]) {
        return cookieLang
      }

      // 3. 检查Accept-Language头部
      const acceptLanguage = event.node.req.headers['accept-language']
      if (acceptLanguage) {
        const preferredLanguages = parseAcceptLanguage(acceptLanguage)
        return matchBestLanguage(preferredLanguages)
      }

      return defaultLanguage
    } catch (error) {
      console.warn('服务端语言检测失败:', error)
      return defaultLanguage
    }
  }

  /**
   * 客户端语言检测
   */
  const detectClientLanguage = (): string => {
    if (process.server) return defaultLanguage

    try {
      // 1. 检查URL路径中的语言
      const path = window.location.pathname
      const pathLang = path.match(/^\/(zh|en|ja)\//)?.[1]
      if (pathLang && supportedLanguages[pathLang]) {
        return pathLang
      }

      // 2. 检查Cookie中的语言偏好
      const userLang = useCookie(LANG_COOKIE_KEY)
      if (userLang.value && supportedLanguages[userLang.value]) {
        return userLang.value
      }

      // 3. 检查浏览器语言设置
      const browserLanguages = [
        navigator.language,
        ...(navigator.languages || [])
      ].map(lang => lang.toLowerCase())

      return matchBestLanguage(browserLanguages)
    } catch (error) {
      console.warn('客户端语言检测失败:', error)
      return defaultLanguage
    }
  }

  /**
   * 获取当前语言
   */
  const getCurrentLanguage = (): string => {
    if (process.server) {
      return detectServerLanguage()
    } else {
      return detectClientLanguage()
    }
  }

  /**
   * 获取语言配置
   */
  const getLanguageConfig = (lang?: string): LanguageConfig => {
    const currentLang = lang || getCurrentLanguage()
    return supportedLanguages[currentLang] || supportedLanguages[defaultLanguage]
  }

  /**
   * 设置语言偏好
   */
  const setLanguagePreference = (lang: string) => {
    if (!supportedLanguages[lang]) {
      console.warn(`不支持的语言: ${lang}`)
      return
    }

    const userLang = useCookie(LANG_COOKIE_KEY, {
      default: () => defaultLanguage,
      maxAge: 60 * 60 * 24 * 365 // 1年
    })
    
    userLang.value = lang
  }

  /**
   * 生成语言切换URL
   */
  const generateLanguageUrl = (targetLang: string, currentPath?: string): string => {
    if (!supportedLanguages[targetLang]) {
      return '/'
    }

    const path = currentPath || (process.client ? window.location.pathname : '/')
    const config = useRuntimeConfig().public
    
    // 移除当前语言前缀
    const cleanPath = path.replace(/^\/(zh|en|ja)/, '')
    
    // 添加新语言前缀
    const newPath = `/${targetLang}${cleanPath || ''}`
    
    // 根据屏幕尺寸选择域名
    if (process.client) {
      const isMobile = window.matchMedia("(max-width: 700px)").matches
      const domain = isMobile ? config.mobile_site_host : config.pc_site_host
      return domain ? `${domain}${newPath}` : newPath
    }
    
    return newPath
  }

  /**
   * 获取所有支持的语言
   */
  const getSupportedLanguages = () => {
    return Object.entries(supportedLanguages).map(([key, config]) => ({
      key,
      ...config
    }))
  }

  return {
    supportedLanguages,
    defaultLanguage,
    getCurrentLanguage,
    getLanguageConfig,
    setLanguagePreference,
    generateLanguageUrl,
    getSupportedLanguages,
    detectServerLanguage,
    detectClientLanguage,
    matchBestLanguage,
    parseAcceptLanguage
  }
}

/**
 * 解析Cookie字符串
 */
function parseCookies(cookieString: string): Record<string, string> {
  const cookies: Record<string, string> = {}
  
  if (!cookieString) return cookies
  
  cookieString.split(';').forEach(cookie => {
    const [name, value] = cookie.trim().split('=')
    if (name && value) {
      cookies[name] = decodeURIComponent(value)
    }
  })
  
  return cookies
}
