/**
 * SEO监控和验证工具
 * 用于检查页面SEO元素的完整性和正确性
 */

export const useSeoMonitoring = () => {
  
  // SEO检查结果接口
  interface SeoCheckResult {
    element: string
    status: 'pass' | 'warning' | 'error'
    message: string
    value?: string
    recommendation?: string
  }
  
  // 检查页面Title
  const checkTitle = (): SeoCheckResult => {
    const title = document.title
    const length = title.length
    
    if (!title) {
      return {
        element: 'Title',
        status: 'error',
        message: 'Missing page title',
        recommendation: 'Add a descriptive title between 50-60 characters'
      }
    }
    
    if (length < 30) {
      return {
        element: 'Title',
        status: 'warning',
        message: `Title too short (${length} characters)`,
        value: title,
        recommendation: 'Consider expanding title to 50-60 characters'
      }
    }
    
    if (length > 60) {
      return {
        element: 'Title',
        status: 'warning',
        message: `Title too long (${length} characters)`,
        value: title,
        recommendation: 'Shorten title to 50-60 characters'
      }
    }
    
    return {
      element: 'Title',
      status: 'pass',
      message: `Title length optimal (${length} characters)`,
      value: title
    }
  }
  
  // 检查Meta Description
  const checkMetaDescription = (): SeoCheckResult => {
    const metaDesc = document.querySelector('meta[name="description"]')?.getAttribute('content') || ''
    const length = metaDesc.length
    
    if (!metaDesc) {
      return {
        element: 'Meta Description',
        status: 'error',
        message: 'Missing meta description',
        recommendation: 'Add a meta description between 120-160 characters'
      }
    }
    
    if (length < 120) {
      return {
        element: 'Meta Description',
        status: 'warning',
        message: `Meta description too short (${length} characters)`,
        value: metaDesc,
        recommendation: 'Expand description to 120-160 characters'
      }
    }
    
    if (length > 160) {
      return {
        element: 'Meta Description',
        status: 'warning',
        message: `Meta description too long (${length} characters)`,
        value: metaDesc,
        recommendation: 'Shorten description to 120-160 characters'
      }
    }
    
    return {
      element: 'Meta Description',
      status: 'pass',
      message: `Meta description length optimal (${length} characters)`,
      value: metaDesc
    }
  }
  
  // 检查H1标签
  const checkH1Tags = (): SeoCheckResult => {
    const h1Tags = document.querySelectorAll('h1')
    
    if (h1Tags.length === 0) {
      return {
        element: 'H1 Tag',
        status: 'error',
        message: 'No H1 tag found',
        recommendation: 'Add exactly one H1 tag to the page'
      }
    }
    
    if (h1Tags.length > 1) {
      return {
        element: 'H1 Tag',
        status: 'warning',
        message: `Multiple H1 tags found (${h1Tags.length})`,
        recommendation: 'Use only one H1 tag per page'
      }
    }
    
    const h1Text = h1Tags[0].textContent || ''
    return {
      element: 'H1 Tag',
      status: 'pass',
      message: 'Single H1 tag found',
      value: h1Text
    }
  }
  
  // 检查图片Alt属性
  const checkImageAltTags = (): SeoCheckResult => {
    const images = document.querySelectorAll('img')
    const imagesWithoutAlt = Array.from(images).filter(img => !img.getAttribute('alt'))
    
    if (imagesWithoutAlt.length === 0) {
      return {
        element: 'Image Alt Tags',
        status: 'pass',
        message: `All ${images.length} images have alt attributes`
      }
    }
    
    return {
      element: 'Image Alt Tags',
      status: 'warning',
      message: `${imagesWithoutAlt.length} of ${images.length} images missing alt attributes`,
      recommendation: 'Add descriptive alt attributes to all images'
    }
  }
  
  // 检查Canonical URL
  const checkCanonicalUrl = (): SeoCheckResult => {
    const canonical = document.querySelector('link[rel="canonical"]')?.getAttribute('href')
    
    if (!canonical) {
      return {
        element: 'Canonical URL',
        status: 'warning',
        message: 'No canonical URL found',
        recommendation: 'Add canonical URL to prevent duplicate content issues'
      }
    }
    
    return {
      element: 'Canonical URL',
      status: 'pass',
      message: 'Canonical URL found',
      value: canonical
    }
  }
  
  // 检查Hreflang标签
  const checkHreflangTags = (): SeoCheckResult => {
    const hreflangTags = document.querySelectorAll('link[rel="alternate"][hreflang]')
    
    if (hreflangTags.length === 0) {
      return {
        element: 'Hreflang Tags',
        status: 'warning',
        message: 'No hreflang tags found',
        recommendation: 'Add hreflang tags for multilingual SEO'
      }
    }
    
    return {
      element: 'Hreflang Tags',
      status: 'pass',
      message: `${hreflangTags.length} hreflang tags found`
    }
  }
  
  // 检查结构化数据
  const checkStructuredData = (): SeoCheckResult => {
    const structuredData = document.querySelectorAll('script[type="application/ld+json"]')
    
    if (structuredData.length === 0) {
      return {
        element: 'Structured Data',
        status: 'warning',
        message: 'No structured data found',
        recommendation: 'Add JSON-LD structured data for better search visibility'
      }
    }
    
    return {
      element: 'Structured Data',
      status: 'pass',
      message: `${structuredData.length} structured data blocks found`
    }
  }
  
  // 执行完整的SEO检查
  const runSeoAudit = (): SeoCheckResult[] => {
    const results: SeoCheckResult[] = []
    
    try {
      results.push(checkTitle())
      results.push(checkMetaDescription())
      results.push(checkH1Tags())
      results.push(checkImageAltTags())
      results.push(checkCanonicalUrl())
      results.push(checkHreflangTags())
      results.push(checkStructuredData())
    } catch (error) {
      console.error('SEO audit failed:', error)
    }
    
    return results
  }
  
  // 生成SEO报告
  const generateSeoReport = (): string => {
    const results = runSeoAudit()
    const passCount = results.filter(r => r.status === 'pass').length
    const warningCount = results.filter(r => r.status === 'warning').length
    const errorCount = results.filter(r => r.status === 'error').length
    
    let report = `SEO Audit Report\n`
    report += `================\n`
    report += `Total checks: ${results.length}\n`
    report += `Passed: ${passCount}\n`
    report += `Warnings: ${warningCount}\n`
    report += `Errors: ${errorCount}\n\n`
    
    results.forEach(result => {
      const status = result.status.toUpperCase()
      report += `[${status}] ${result.element}: ${result.message}\n`
      if (result.value) {
        report += `  Value: ${result.value}\n`
      }
      if (result.recommendation) {
        report += `  Recommendation: ${result.recommendation}\n`
      }
      report += '\n'
    })
    
    return report
  }
  
  // 在控制台显示SEO报告
  const logSeoReport = () => {
    const report = generateSeoReport()
    console.log(report)
  }
  
  return {
    checkTitle,
    checkMetaDescription,
    checkH1Tags,
    checkImageAltTags,
    checkCanonicalUrl,
    checkHreflangTags,
    checkStructuredData,
    runSeoAudit,
    generateSeoReport,
    logSeoReport
  }
}
