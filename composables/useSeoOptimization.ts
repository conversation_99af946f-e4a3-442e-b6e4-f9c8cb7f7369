// SEO优化通用函数
export const useSeoOptimization = () => {

  // 获取语言检测功能
  const { getCurrentLanguage, getLanguageConfig, generateLanguageUrl } = useLanguageDetection()

  // 生成页面SEO配置（增强版，支持自动语言检测和关键词优化）
  const generatePageSeo = (config: {
    title: string
    description: string
    keywords?: string
    lang?: string  // 现在是可选的，会自动检测
    path: string
    image?: string
    type?: string
    autoDetectLanguage?: boolean  // 是否自动检测语言
    pageType?: string  // 页面类型，用于自动生成关键词
  }) => {
    // 自动检测语言或使用提供的语言
    const detectedLang = config.autoDetectLanguage !== false ? getCurrentLanguage() : (config.lang || 'zh')
    const { title, description, path, image, type = 'website', pageType } = config
    const lang = config.lang || detectedLang

    // 使用提供的关键词或默认关键词
    const keywords = config.keywords || ''

    const baseUrl = 'https://www.smartdeer.work'  // 使用正确的域名
    const fullUrl = `${baseUrl}${path}`
    const defaultImage = `${baseUrl}/images/tg_banner.png`

    // 智能多语言URL映射
    const langUrls = {
      en: generateLanguageUrl('en', path),
      zh: generateLanguageUrl('zh', path),
      ja: generateLanguageUrl('ja', path)
    }
    
    return {
      htmlAttrs: { lang: lang === 'zh' ? 'zh-CN' : lang === 'ja' ? 'ja-JP' : 'en-US' },
      title,
      meta: [
        // 基础SEO
        { name: 'description', content: description },
        { name: 'keywords', content: keywords || '' },
        { name: 'author', content: 'SmartDeer' },
        { name: 'robots', content: 'index, follow' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },
        
        // Canonical URL
        { rel: 'canonical', href: fullUrl },
        
        // 多语言标签
        { rel: 'alternate', hreflang: 'en', href: `${baseUrl}${langUrls.en}` },
        { rel: 'alternate', hreflang: 'zh-CN', href: `${baseUrl}${langUrls.zh}` },
        { rel: 'alternate', hreflang: 'ja-JP', href: `${baseUrl}${langUrls.ja}` },
        { rel: 'alternate', hreflang: 'x-default', href: `${baseUrl}${langUrls.en}` },
        
        // Open Graph
        { property: 'og:title', content: title },
        { property: 'og:description', content: description },
        { property: 'og:type', content: type },
        { property: 'og:url', content: fullUrl },
        { property: 'og:site_name', content: 'SmartDeer' },
        { property: 'og:image', content: image || defaultImage },
        { property: 'og:image:width', content: '1200' },
        { property: 'og:image:height', content: '630' },
        { property: 'og:locale', content: lang === 'zh' ? 'zh_CN' : lang === 'ja' ? 'ja_JP' : 'en_US' },
        
        // Twitter Card
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:site', content: '@SmartDeer' },
        { name: 'twitter:title', content: title },
        { name: 'twitter:description', content: description },
        { name: 'twitter:image', content: image || defaultImage },
        { name: 'twitter:image:alt', content: title },
        
        // 百度特定优化
        { name: 'applicable-device', content: 'pc,mobile' },
        { name: 'MobileOptimized', content: 'width' },
        { name: 'HandheldFriendly', content: 'true' }
      ]
    }
  }
  
  // 生成文章页面SEO
  const generateArticleSeo = (config: {
    title: string
    description: string
    lang: string
    path: string
    publishDate?: string
    modifiedDate?: string
    author?: string
    image?: string
  }) => {
    const baseSeo = generatePageSeo({
      ...config,
      type: 'article'
    })
    
    // 添加文章特定的meta标签
    const articleMeta = [
      { property: 'article:published_time', content: config.publishDate || new Date().toISOString() },
      { property: 'article:modified_time', content: config.modifiedDate || new Date().toISOString() },
      { property: 'article:author', content: config.author || 'SmartDeer' },
      { property: 'article:section', content: 'Global Employment' },
      { property: 'article:tag', content: 'EOR, Global HR, International Recruitment' }
    ]
    
    return {
      ...baseSeo,
      meta: [...baseSeo.meta, ...articleMeta]
    }
  }
  
  // 生成关键词建议
  const getKeywordSuggestions = (lang: string, pageType: string) => {
    const keywords = {
      en: {
        homepage: 'global employment, international recruitment, EOR services, global payroll, HR outsourcing, overseas expansion, compliance management, international hiring, global HR solutions, employer of record',
        calculator: 'employee cost calculator, global payroll calculator, international hiring costs, EOR cost calculator, global employment cost, salary calculator international',
        aboutus: 'SmartDeer company, global HR company, international employment services, EOR provider, global recruitment agency, HR outsourcing company'
      },
      zh: {
        homepage: '全球雇佣, 全球招聘, 海外雇佣, EOR服务, 国际薪酬, 人力资源外包, 出海企业, 合规管理, 海外人事, 跨境用工, 雇主责任',
        calculator: '员工成本计算器, 全球薪酬计算器, 国际招聘成本, EOR成本计算, 全球雇佣成本, 海外薪资计算器',
        aboutus: 'SmartDeer公司, 全球人力资源公司, 国际雇佣服务, EOR服务商, 全球招聘机构, 人力资源外包公司'
      },
      ja: {
        homepage: 'グローバル雇用, 国際採用, EORサービス, 海外給与管理, 人事アウトソーシング, 海外展開, コンプライアンス, 国際人事, グローバル人材, 雇用主責任',
        calculator: '従業員コスト計算機, グローバル給与計算機, 国際採用コスト, EORコスト計算, グローバル雇用コスト, 海外給与計算機',
        aboutus: 'SmartDeer会社, グローバル人事会社, 国際雇用サービス, EORプロバイダー, グローバル採用代行, 人事アウトソーシング会社'
      }
    }
    
    return keywords[lang as keyof typeof keywords]?.[pageType as keyof typeof keywords['en']] || keywords.en.homepage
  }
  
  // 智能SEO生成（自动检测语言和内容）
  const generateSmartSeo = (config: {
    path: string
    title?: string
    description?: string
    keywords?: string
    image?: string
    type?: string
  }) => {
    const detectedLang = getCurrentLanguage()
    const languageConfig = getLanguageConfig(detectedLang)

    // 如果没有提供内容，根据路径和语言自动生成
    const autoContent = generateAutoSeoContent(config.path, detectedLang)

    return generatePageSeo({
      title: config.title || autoContent.title,
      description: config.description || autoContent.description,
      keywords: config.keywords || autoContent.keywords,
      lang: detectedLang,
      path: config.path,
      image: config.image,
      type: config.type,
      autoDetectLanguage: true
    })
  }

  // 根据路径和语言自动生成SEO内容
  const generateAutoSeoContent = (path: string, lang: string) => {
    // 页面类型检测
    const pageType = detectPageType(path)

    // 基础SEO模板
    const seoTemplates = {
      homepage: {
        zh: {
          title: 'SmartDeer - 全球招聘雇佣，海外人力资源一站式服务平台',
          description: 'SmartDeer提供全球招聘、海外雇佣、薪酬管理等一站式人力资源服务，覆盖150+国家，助力企业全球化扩张。',
          keywords: '全球招聘,海外雇佣,人力资源外包,EOR,PEO,全球薪酬,海外用工,国际招聘'
        },
        en: {
          title: 'SmartDeer - AI-Powered Global Employment & HR Solutions',
          description: 'SmartDeer provides AI-powered global employment, intelligent recruitment, and smart payroll services across 150+ countries for business expansion.',
          keywords: 'AI-powered global recruitment,intelligent international employment,smart HR outsourcing,automated EOR,AI-driven PEO,intelligent global payroll,AI international hiring,generative AI for HR'
        },
        ja: {
          title: 'SmartDeer - グローバル採用・雇用ソリューション',
          description: 'SmartDeerは150カ国以上でグローバル雇用、採用、給与管理の包括的サービスを提供。',
          keywords: 'グローバル採用,国際雇用,人事アウトソーシング,EOR,PEO,グローバル給与,国際採用'
        }
      },
      calculator: {
        zh: {
          title: '全球薪酬成本计算器 - SmartDeer',
          description: '免费使用SmartDeer全球薪酬成本计算器，快速计算150+国家的员工雇佣成本。',
          keywords: '薪酬计算器,全球薪酬,员工成本,雇佣成本,国际薪资'
        },
        en: {
          title: 'AI-Powered Global Payroll Calculator - SmartDeer',
          description: 'Free AI-powered global payroll cost calculator. Intelligent estimation of employment costs across 150+ countries.',
          keywords: 'AI payroll calculator,intelligent global payroll,smart employee cost calculator,AI employment cost estimator,automated payroll calculator'
        },
        ja: {
          title: 'グローバル給与コスト計算機 - SmartDeer',
          description: 'SmartDeerの無料グローバル給与コスト計算機で、150カ国以上の従業員雇用コストを計算。',
          keywords: '給与計算機,グローバル給与,従業員コスト,雇用コスト,国際給与'
        }
      }
    }

    const template = seoTemplates[pageType as keyof typeof seoTemplates]
    return template?.[lang as keyof typeof template] || template?.zh || seoTemplates.homepage.zh
  }

  // 检测页面类型
  const detectPageType = (path: string): string => {
    if (path.includes('/calculator')) return 'calculator'
    if (path.includes('/aboutus')) return 'aboutus'
    if (path.includes('/articles')) return 'articles'
    if (path.includes('/countries')) return 'countries'
    if (path.includes('/legal')) return 'legal'
    if (path.includes('/marketing')) return 'marketing'
    return 'homepage'
  }

  return {
    generatePageSeo,
    generateArticleSeo,
    getKeywordSuggestions,
    generateSmartSeo,
    generateAutoSeoContent,
    detectPageType
  }
}
