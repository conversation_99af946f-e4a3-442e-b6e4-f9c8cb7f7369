/**
 * Referrer检测和管理composable
 * 处理用户来源检测，支持搜索引擎关键词提取和来源分析
 */

export interface ReferrerInfo {
  domain: string
  keyword: string
  source: string
  isSearchEngine: boolean
  isDirectAccess: boolean
}

export const useReferrerDetection = () => {
  const USER_REF_KEY = 'USER_REF'
  const REFERRER_INFO_KEY = 'REFERRER_INFO'
  
  // 搜索引擎配置
  const searchEngines = {
    'google.com': { param: 'q', name: 'Google' },
    'google.co.jp': { param: 'q', name: 'Google Japan' },
    'google.co.uk': { param: 'q', name: 'Google UK' },
    'google.ca': { param: 'q', name: 'Google Canada' },
    'google.com.au': { param: 'q', name: 'Google Australia' },
    'baidu.com': { param: 'wd', name: '百度' },
    'bing.com': { param: 'q', name: 'Bing' },
    'yahoo.com': { param: 'p', name: 'Yahoo' },
    'duckduckgo.com': { param: 'q', name: 'DuckDuckGo' },
    'yandex.ru': { param: 'text', name: 'Yandex' },
    'yandex.com': { param: 'text', name: 'Yandex' },
    'sogou.com': { param: 'query', name: '搜狗' },
    'so.com': { param: 'q', name: '360搜索' }
  }

  /**
   * 检测并存储referrer信息
   * 在页面加载时调用，确保referrer信息被正确保存
   */
  const detectAndStoreReferrer = (): ReferrerInfo => {
    if (process.server) {
      return getEmptyReferrerInfo()
    }

    try {
      // 首先检查URL参数中是否有referrer信息（来自语言跳转）
      const urlParams = new URLSearchParams(window.location.search)
      const refParam = urlParams.get('ref')
      const kwParam = urlParams.get('kw')
      const srcParam = urlParams.get('src')
      
      if (refParam) {
        console.log('🔗 从URL参数中检测到referrer信息')
        const referrerInfo: ReferrerInfo = {
          domain: decodeURIComponent(refParam),
          keyword: kwParam ? decodeURIComponent(kwParam) : '',
          source: srcParam ? decodeURIComponent(srcParam) : '',
          isSearchEngine: false,
          isDirectAccess: false
        }
        
        // 根据domain判断是否是搜索引擎
        for (const [domain, config] of Object.entries(searchEngines)) {
          if (referrerInfo.domain.includes(domain)) {
            referrerInfo.isSearchEngine = true
            referrerInfo.source = config.name
            break
          }
        }
        
        // 检查是否是直接访问
        if (referrerInfo.domain === 'direct' || referrerInfo.domain.includes('smartdeer.work')) {
          referrerInfo.isDirectAccess = true
          referrerInfo.source = 'direct'
        }
        
        console.log('✅ 从URL参数解析的referrer信息:', referrerInfo)
        storeReferrerInfo(referrerInfo)
        
        // 清除URL参数，避免重复处理
        const newUrl = new URL(window.location.href)
        newUrl.searchParams.delete('ref')
        newUrl.searchParams.delete('kw')
        newUrl.searchParams.delete('src')
        window.history.replaceState({}, '', newUrl.toString())
        
        return referrerInfo
      }

      // 检查是否已经存储了referrer信息
      const existingInfo = getStoredReferrerInfo()
      if (existingInfo && existingInfo.domain) {
        console.log('📋 使用已存储的referrer信息:', existingInfo)
        return existingInfo
      }

      // 获取当前referrer
      const currentReferrer = document.referrer
      console.log('🔍 检测到当前referrer:', currentReferrer)

      if (!currentReferrer) {
        console.log('⚠️ 没有referrer信息，可能是直接访问')
        const directInfo = getEmptyReferrerInfo()
        directInfo.isDirectAccess = true
        directInfo.source = 'direct'
        storeReferrerInfo(directInfo)
        return directInfo
      }

      // 解析referrer
      const parsedInfo = parseReferrer(currentReferrer)
      console.log('✅ 解析后的referrer信息:', parsedInfo)
      
      // 存储原始referrer和解析后的信息
      const encodedReferrer = encodeURIComponent(currentReferrer)
      sessionStorage.setItem(USER_REF_KEY, encodedReferrer)
      storeReferrerInfo(parsedInfo)
      
      return parsedInfo
    } catch (error) {
      console.error('❌ referrer检测失败:', error)
      const errorInfo = getEmptyReferrerInfo()
      storeReferrerInfo(errorInfo)
      return errorInfo
    }
  }

  /**
   * 解析referrer URL
   */
  const parseReferrer = (referrerUrl: string): ReferrerInfo => {
    try {
      const url = new URL(referrerUrl)
      const hostname = url.hostname.toLowerCase()
      
      // 检查是否是自己的网站
      if (hostname.includes('smartdeer.work')) {
        return {
          domain: 'direct',
          keyword: '',
          source: 'direct',
          isSearchEngine: false,
          isDirectAccess: true
        }
      }

      // 检查是否是搜索引擎
      for (const [domain, config] of Object.entries(searchEngines)) {
        if (hostname.includes(domain)) {
          const params = new URLSearchParams(url.search)
          const keyword = params.get(config.param) || ''
          
          return {
            domain: hostname,
            keyword: keyword,
            source: config.name,
            isSearchEngine: true,
            isDirectAccess: false
          }
        }
      }

      // 其他网站
      return {
        domain: hostname,
        keyword: '',
        source: 'other',
        isSearchEngine: false,
        isDirectAccess: false
      }
    } catch (error) {
      console.error('❌ 解析referrer URL失败:', error)
      return getEmptyReferrerInfo()
    }
  }

  /**
   * 获取存储的referrer信息
   */
  const getStoredReferrerInfo = (): ReferrerInfo | null => {
    if (process.server) return null

    try {
      const stored = sessionStorage.getItem(REFERRER_INFO_KEY)
      if (stored) {
        return JSON.parse(stored)
      }
    } catch (error) {
      console.error('❌ 读取存储的referrer信息失败:', error)
    }
    return null
  }

  /**
   * 存储referrer信息
   */
  const storeReferrerInfo = (info: ReferrerInfo): void => {
    if (process.server) return

    try {
      sessionStorage.setItem(REFERRER_INFO_KEY, JSON.stringify(info))
      console.log('💾 已存储referrer信息:', info)
    } catch (error) {
      console.error('❌ 存储referrer信息失败:', error)
    }
  }

  /**
   * 获取当前referrer信息（用于表单提交等场景）
   */
  const getCurrentReferrerInfo = (): ReferrerInfo => {
    if (process.server) {
      return getEmptyReferrerInfo()
    }

    // 首先尝试从存储中获取
    const storedInfo = getStoredReferrerInfo()
    if (storedInfo && storedInfo.domain) {
      return storedInfo
    }

    // 如果没有存储的信息，尝试重新检测
    return detectAndStoreReferrer()
  }

  /**
   * 获取原始referrer URL（用于向后兼容）
   */
  const getOriginalReferrer = (): string => {
    if (process.server) return ''

    try {
      const stored = sessionStorage.getItem(USER_REF_KEY)
      if (stored) {
        return decodeURIComponent(stored)
      }
    } catch (error) {
      console.error('❌ 读取原始referrer失败:', error)
    }
    return ''
  }

  /**
   * 清除referrer信息
   */
  const clearReferrerInfo = (): void => {
    if (process.server) return

    sessionStorage.removeItem(USER_REF_KEY)
    sessionStorage.removeItem(REFERRER_INFO_KEY)
    console.log('🗑️ 已清除referrer信息')
  }

  /**
   * 获取空的referrer信息
   */
  const getEmptyReferrerInfo = (): ReferrerInfo => {
    return {
      domain: '',
      keyword: '',
      source: '',
      isSearchEngine: false,
      isDirectAccess: false
    }
  }

  /**
   * 格式化referrer信息用于显示
   */
  const formatReferrerForDisplay = (info: ReferrerInfo): { domain: string; keyword: string } => {
    return {
      domain: info.domain || 'unknown',
      keyword: info.keyword || ''
    }
  }

  return {
    detectAndStoreReferrer,
    getCurrentReferrerInfo,
    getOriginalReferrer,
    clearReferrerInfo,
    formatReferrerForDisplay,
    parseReferrer,
    searchEngines
  }
}
