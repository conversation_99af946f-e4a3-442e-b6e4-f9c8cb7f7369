<template lang="pug">
.site-footer
  .site-footer-wrapper(v-if="lang == 'zh'")
    .section.logo
      .footer-logo
        figure
          img(src="~/assets/images/index/footer-logo.png" alt="~/assets/images/index/footer-logo.png")
      .email(@click="handleMailsome") <EMAIL>
      .footer-slogon 
        span.pointer(@click="handleClickTo('https://www.smartdeer.com/')") 灵鹿聘·全球人才招聘
        a(href="/zh/aboutus") 关于我们
        span(@click="() => {$emit('contactUs')}") 联系我们
      .compliance
        img(src="~/assets/images/index/iso27001.png" style="margin-right: 20px;")
        img(src="~/assets/images/index/cyberport.png" alt="~/assets/images/index/cyberport.png")
      .social
        .text 加入社区
        .imgs
          figure(@click="handleClickTo('https://twitter.com/SmartDeerGlobal')")
            img.d(src="~/assets/images/index/twitter.svg")
            img.h(src="~/assets/images/index/twitter-h.svg")
          figure(@click="handleClickTo('https://t.me/SmartDeerGlobal')")
            img.d(src="~/assets/images/index/telegram.svg")
            img.h(src="~/assets/images/index/telegram-h.svg")
          figure(@click="handleClickTo('https://www.linkedin.com/company/smartdeer-global/-global/career/')")
            img.d(src="~/assets/images/index/LinkedIn.svg")
            img.h(src="~/assets/images/index/LinkedIn-h.svg")
        //- .wechat-name 灵鹿聘全球招聘小程序
        //- .usage 请使用微信扫描二维码
  
    
    .spliter
    
    .section.compliance
      div
        span © 2022 SMARTDEER
        span 北京谊桥管理科技有限责任公司版权所有
      div
        NuxtLink(to="https://beian.miit.gov.cn/") 京ICP备**********号-1
        NuxtLink(to="/compliance/hr-service-license") 人力资源许可证
        NuxtLink(to="/compliance/business-license") 营业执照
        NuxtLink(to="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010102005860") 京公网安备 11010102005860 号
      
      client-only
        el-dropdown.language-selector
          .text
            div 中文
            figure
              img(src="~/assets/images/index/trangle.svg" alt="~/assets/images/index/trangle.svg")

          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(@click="switchLang('zh')") 中文
              el-dropdown-item(@click="switchLang('en')") English
              el-dropdown-item(@click="switchLang('ja')") 日本語

  .site-footer-wrapper.helvetica(v-if="lang == 'en'")
    .section.logo
      .footer-logo
        figure
          img(src="~/assets/images/index/footer-logo.png" alt="~/assets/images/index/footer-logo.png")
      .email(@click="handleMailsome") <EMAIL>
      .footer-slogon 
        span.pointer(@click="handleClickTo('https://www.smartdeer.com/')") Smartdeer Global Recruitment
        a(href="/en/aboutus") About Us
        span(@click="() => {$emit('contactUs')}") Contact Us
      .compliance
        img(src="~/assets/images/index/iso27001.png" style="margin-right: 20px;")
        img(src="~/assets/images/index/cyberport.png" alt="~/assets/images/index/cyberport.png")
      .social
        .text Join the community
        .imgs
          figure(@click="handleClickTo('https://twitter.com/SmartDeerGlobal')")
            img.d(src="~/assets/images/index/twitter.svg")
            img.h(src="~/assets/images/index/twitter-h.svg")
          figure(@click="handleClickTo('https://t.me/SmartDeerGlobal')")
            img.d(src="~/assets/images/index/telegram.svg")
            img.h(src="~/assets/images/index/telegram-h.svg")
          figure(@click="handleClickTo('https://www.linkedin.com/company/smartdeer-global/-global/career/')")
            img.d(src="~/assets/images/index/LinkedIn.svg")
            img.h(src="~/assets/images/index/LinkedIn-h.svg")
       

    .spliter
    .section.compliance
      div
        span Copyright© 2022 Beijing Yiqiao LLC All Rights Reserved
      div
        NuxtLink(to="/compliance/hr-service-license") Human Resources License
        NuxtLink(to="/compliance/business-license") Business License

      client-only
        el-dropdown.language-selector
          .text
            div English
            figure
              img(src="~/assets/images/index/trangle.svg" alt="~/assets/images/index/trangle.svg")

          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(@click="switchLang('zh')") 中文
              el-dropdown-item(@click="switchLang('en')") English
              el-dropdown-item(@click="switchLang('ja')") 日本語

  .site-footer-wrapper.helvetica(v-if="lang == 'ja'")
    .section.logo
      .footer-logo
        figure
          img(src="~/assets/images/index/footer-logo.png" alt="~/assets/images/index/footer-logo.png")
      .email(@click="handleMailsome") <EMAIL>
      .footer-slogon 
        span.pointer(@click="handleClickTo('https://www.smartdeer.com/')") サマートディアー
        a(href="/ja/aboutus") 会社概要
        span(@click="() => {$emit('contactUs')}") お問い合わせ
      .compliance
        img(src="~/assets/images/index/iso27001.png" style="margin-right: 20px;")
        img(src="~/assets/images/index/cyberport.png" alt="~/assets/images/index/cyberport.png")
      .social
        .text コミュニティ参加
        .imgs
          figure(@click="handleClickTo('https://twitter.com/SmartDeerGlobal')")
            img.d(src="~/assets/images/index/twitter.svg")
            img.h(src="~/assets/images/index/twitter-h.svg")
          figure(@click="handleClickTo('https://t.me/SmartDeerGlobal')")
            img.d(src="~/assets/images/index/telegram.svg")
            img.h(src="~/assets/images/index/telegram-h.svg")
          figure(@click="handleClickTo('https://www.linkedin.com/company/smartdeer-global/-global/career/')")
            img.d(src="~/assets/images/index/LinkedIn.svg")
            img.h(src="~/assets/images/index/LinkedIn-h.svg")

    .spliter
    .section.compliance
      div
        span © 2025 北京谊桥管理科技有限责任公司 版权所有
      div
        NuxtLink(to="/compliance/hr-service-license") 人材派遣許可証
        NuxtLink(to="/compliance/business-license") 事業許可証

      client-only
        el-dropdown.language-selector
          .text
            div 日本語
            figure
              img(src="~/assets/images/index/trangle.svg" alt="~/assets/images/index/trangle.svg")

          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(@click="switchLang('zh')") 中文
              el-dropdown-item(@click="switchLang('en')") English
              el-dropdown-item(@click="switchLang('ja')") 日本語

  
  .overlay(v-show="showDialog")
    .dialog(v-if="lang == 'zh'")
      .title 灵鹿聘·全球人才招聘
        figure.dialog-close(@click="showDialog = false")
          img(src="~/assets/images/index/close.svg" alt="~/assets/images/index/close.svg")
      .content 
        .col
          .col-title 全球社招
          .img-box
            figure.wechat-qr
              img(src="~/assets/images/index/wechat-qr.jpg" alt="~/assets/images/index/wechat-qr.jpg")
            .text 请使用微信扫描二维码

        .col
          .col-title 全球校招
          .img-box.hover(@click="handleClickTo('http://www.smartdeer.co/')")
            figure.guide-pc
              img(src="~/assets/images/index/guide-pc.svg" alt="~/assets/images/index/guide-pc.svg")
            .link http://www.smartdeer.co/
    
    .dialog(v-if="lang == 'en'")
      .title Smartdeer Global Recruitment
        figure.dialog-close(@click="showDialog = false")
          img(src="~/assets/images/index/close.svg" alt="~/assets/images/index/close.svg")
      .content 
        .col
          .col-title For White Collar Recruitment
          .img-box
            figure.wechat-qr
              img(src="~/assets/images/index/wechat-qr.jpg" alt="~/assets/images/index/wechat-qr.jpg")
            .text Please use WeChat to scan the QR code

        .col
          .col-title For Student Recruitment
          .img-box.hover(@click="handleClickTo('http://www.smartdeer.co/')")
            figure.guide-pc
              img(src="~/assets/images/index/guide-pc.svg" alt="~/assets/images/index/guide-pc.svg")
            .link http://www.smartdeer.co/

    .dialog(v-if="lang == 'ja'")
      .title サマートディアー
        figure.dialog-close(@click="showDialog = false")
          img(src="~/assets/images/index/close.svg" alt="~/assets/images/index/close.svg")
      .content 
        .col
          .col-title グローバル社招
          .img-box
            figure.wechat-qr
              img(src="~/assets/images/index/wechat-qr.jpg" alt="~/assets/images/index/wechat-qr.jpg")
            .text ウェブサイトを開く

        .col
          .col-title グローバル校招 
          .img-box.hover(@click="handleClickTo('http://www.smartdeer.co/')")
            figure.guide-pc
              img(src="~/assets/images/index/guide-pc.svg" alt="~/assets/images/index/guide-pc.svg")
            .link http://www.smartdeer.co/


</template>
  
<script lang="ts" setup>
import { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import langTool from '~/assets/utils/lang'
const props = defineProps(['lang'])
const { lang } = toRefs(props)

const showDialog = ref(false)

const handleClickTo = (url) => {
  window.open(url)
  // window.open('https://www.smartdeer.co')
}
function switchLang(lang) {
  const pathName = window.location.pathname
  const newPathName = pathName.substring(3)

  langTool.swithLang(lang, newPathName)
}

// js调用本地邮箱发送邮件
const handleMailsome = () => {
  parent.location.href = 'mailto:<EMAIL>';
}
</script>
  
<style lang="scss" scoped>
@import url('@/assets/styles/en.scss');

.helvetica{
  font-family: Helvetica;
}

.site-footer {
  background-color: #272A30;
  padding: 50px 0 34px 0;

  .pointer{
    cursor: pointer;
    user-select: none;
  }

  .site-footer-wrapper {
    width: 1204px;
    margin: 0 auto;
    color: #FFF;
    box-sizing: border-box;

    .section {
      font-size: 18px;

      a,
      a:active {
        color: inherit;
        text-decoration: none;
      }
    }

    .section.logo {
      padding-bottom: 32px;
      position: relative;

      .footer-logo {
        figure{
          width: 219px;

          img {
            width: 100%;
          }
        }
      }

      .email{
        margin-top: 14px;
        height: 22px;
        font-size: 18px;
        font-family: Helvetica;
        line-height: 22px;
        cursor: pointer;

        &:hover{
          color: #FF8600;
        }
      }

      .footer-slogon {
        margin-top: 29px;
        font-size: 18px;
        font-size: 18px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 25px;

        span, a {
          margin-right: 47px;
          cursor: pointer;

          &:hover{
            color: #FF8600;
          }
        }
      }

      .compliance {
        position: absolute;
        top: 0;
        right: 0;
        img {
          display: inline-block;
          width: 70px;
          height: 70px;
        }
      }
      .social {
        position: absolute;
        right: 0;
        top: 80px;

        .text {
          font-size: 18px;
          font-weight: 300;
          color: #99A6BF;
          line-height: 22px;
          text-align: center;
        }

        .imgs {
          display: flex;
          margin-top: 10px;
          justify-content: space-between;
          width: 156px;

          figure {
            cursor: pointer;

            &:hover{
              .d {
                display: none;
              }
              .h {
                display: block;
              }
            }

            img {
              width: 40px;
              height: 40px;
            }

            .h {
              display: none;
            }
          }
        }
      }
    }
  }

  .section.compliance {
    margin-top: 22px;
    font-size: 18px;
    font-family: Helvetica-Light, Helvetica;
    font-weight: 300;
    color: #99A6BF;
    line-height: 22px;
    position: relative;

    span,
    a {
      margin-right: 15px;
    }
  }

  .spliter {
    display: block;
    border-bottom: 1px solid #424752;
  }

  .language-selector{
    position: absolute;
    top: 0;
    right: 0;
    border-radius: 4px;
    border: 2px solid #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;

    .text{
      width: 105px;
      height: 40px;
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      figure{
        width: 8px;
        margin-left: 10px;
      }
    }
  }
}

.overlay {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  top: 0;
  left: 0;
  background: rgba(0,0,0,0.4);

  .dialog {
    width: 600px;
    height: 478px;
    background: #FFFFFF;
    border-radius: 4px;
    position: fixed;
    z-index: 101;
    top: 50%;
    left: 50%;
    margin-top: -239px;
    margin-left: -300px;
    padding-top: 80px;
    box-sizing: border-box;

    .title {
      font-size: 32px;
      font-weight: 500;
      color: #333333;
      line-height: 45px;
      text-align: center;
    }

    .dialog-close {
      padding: 20px 22px;
      width: 14px;
      height: 14px;
      position: absolute;
      left: 0;
      top: 0;

      img {
        cursor: pointer;
      }
    }

    .content {
      display: flex;
      margin-top: 48px;
      padding: 0 69px;

      .col {
        flex: 1;
        text-align: center;

        &-title {
          font-size: 14px;
          font-weight: bold;
          color: #454545;
          line-height: 19px;
          margin-bottom: 19px;
        }

        .img-box {
          width: 170px;
          height: 187px;
          background: #F7F8FA;
          border: 1px solid #F7F8FA;
          box-sizing: border-box;
          padding: 24px 20px;
          border-radius: 8px;
          margin: 0 auto;
          box-sizing: border-box;

          &.hover {
            cursor: pointer;
            &:hover {
              border: 1px solid #FF8600;
            }
          }

          img {
            width: 100%;
          }

          .wechat-qr {
            width: 94px;
            height: 94px;
            margin: 0 auto 11px auto;
            border-radius: 8px;
            border: 1px solid #454545;
            padding: 3px;
            background: #FFFFFF;
          }

          .guide-pc {
            width: 100px;
            height: 100px;
            margin: 0px auto 24px auto;
          }

          .text {
            font-size: 12px;
            font-weight: 300;
            color: rgba(0,0,0,0.85);
            line-height: 14px;
          }

          .link {
            font-size: 12px;
            font-weight: 300;
            color: rgba(0,0,0,0.85);
            // color: #FF8600;
            line-height: 14px;
            cursor: pointer;
            text-decoration: underline;
          }
        }
      }
    }
  }
}
</style>