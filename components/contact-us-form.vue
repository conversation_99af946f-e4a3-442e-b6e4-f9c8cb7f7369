<template lang="pug">
.contact-us-form
  template(v-if="lang=='zh'")
    .form-title 联系我们
    .form-body
      el-form(:model="form" ref="ruleFormRef")
        el-form-item(:rules="[{required: true, message:'我们如何称呼您？'}]", prop="name")
          el-input(placeholder="姓名*" size="large" v-model="form.name")

        el-form-item(:rules="[{required: true, message:'请输入您的公司'}]", prop="company")
          el-input(placeholder="公司*" size="large" v-model="form.company")

        el-form-item(:rules="[{required: true, message:'请选择您要咨询的服务'}]", prop="service")
          el-select(size="large" placeholder="所需服务*" v-model="form.service" style="width:100%;")
            el-option(v-for="(item, index) in serviceOptions" :key="index" :label="item.zh" :value="item.zh")

        el-form-item(:rules="[{required:true, message:'我们如何通过邮箱联系您？'}, {type:'email', message:'请确认您输入的是正确的邮箱。'}]", prop="email")
          el-input(placeholder="联系邮箱*" size="large" v-model="form.email")

        el-form-item(:rules="[]", prop="mobile")
          el-input(placeholder="联系电话*" size="large" v-model="form.mobile")
            template(#prepend)
              el-select(v-model="form.countryCode" size="large" style="width: 96px;")
                el-option(v-for="(item, index) in phoneArea" :key="index" :label="item.number" :value="item.number")
                  span {{`${item.number} ${item.areaNameCN}`}}

        el-form-item(:rules="[{required: false, message:'请填写您的联系方式'}]", prop="extra")
          el-input(type="textarea" v-model="form.extra" placeholder="需要我们为您提供什么服务？" rows="6")

        el-form-item()
          el-button(type="primary" style="width: 100%" size="large" @click="submitForm" :loading="status.loading") 提交

  template(v-else-if="lang=='en'")
    .form-title Contact Us
    .form-body
      el-form(:model="form" ref="ruleFormRef")
        el-form-item(:rules="[{required: true }]", prop="name")
          el-input(placeholder="First Name*" size="large" v-model="form.name")

        el-form-item(:rules="[{required: true}]", prop="company")
          el-input(placeholder="Company Name*" size="large" v-model="form.company")

        el-form-item(:rules="[{required: true}]", prop="service")
          el-select(size="large" placeholder="Required Services*" v-model="form.service" style="width:100%;")
            el-option(v-for="(item, index) in serviceOptions" :key="index" :label="item.en" :value="item.zh")

        el-form-item(:rules="[{required:true}, {type:'email'}]", prop="email")
          el-input(placeholder="Email Address*" size="large" v-model="form.email")

        el-form-item(:rules="[]", prop="mobile")
          el-input(placeholder="Phone Number*" size="large" v-model="form.mobile")
            template(#prepend)
              el-select(v-model="form.countryCode" size="large" style="width: 96px;")
                el-option(v-for="(item, index) in phoneArea" :key="index" :label="item.number" :value="item.number")
                  span {{`${item.number} ${item.areaNameEN}`}}

        el-form-item(:rules="[{required: false}]", prop="extra")
          el-input(type="textarea" v-model="form.extra" placeholder="Please describe your requirements and we will contact you later" rows="6")

        el-form-item()
          el-button(type="primary" style="width: 100%" size="large" @click="submitForm" :loading="status.loading") Submit

  template(v-else-if="lang=='ja'")
    .form-title お問い合わせ
    .form-body
      el-form(:model="form" ref="ruleFormRef")
        el-form-item(:rules="[{required: true}]", prop="name")
          el-input(placeholder="お名前*" size="large" v-model="form.name")

        el-form-item(:rules="[{required: true}]", prop="company")
          el-input(placeholder="会社名*" size="large" v-model="form.company")

        el-form-item(:rules="[{required: true}]", prop="service")
          el-select(size="large" placeholder="ご希望のサービス*" v-model="form.service" style="width:100%;")
            el-option(v-for="(item, index) in serviceOptions" :key="index" :label="item.ja" :value="item.zh")

        el-form-item(:rules="[{required: true}, {type:'email'}]", prop="email")
          el-input(placeholder="メールアドレス*" size="large" v-model="form.email")

        el-form-item(:rules="[]", prop="mobile")
          el-input(placeholder="電話番号*" size="large" v-model="form.mobile")
            template(#prepend)
              el-select(v-model="form.countryCode" size="large" style="width: 96px;")
                el-option(v-for="(item, index) in phoneArea" :key="index" :label="item.number" :value="item.number")
                  span {{`${item.number} ${item.areaNameJP}`}}

        el-form-item(:rules="[{required: false}]", prop="extra")
          el-input(type="textarea" v-model="form.extra" placeholder="ご希望のサービス内容をご記入ください" rows="6")

        el-form-item()
          el-button(type="primary" style="width: 100%" size="large" @click="submitForm" :loading="status.loading") 送信する

</template>

<script lang="ts" setup>
import { ElDialog, ElForm, ElFormItem, ElButton, ElInput, ElSelect, ElOption, ElMessage, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import phoneArea from '~/assets/utils/global-phone-area'
import {useCookie} from "#app";
const { lang, textareaResize } = defineProps({
  lang: {
    type: String,
    default: ''
  },
  textareaResize: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['submit'])

const ruleFormRef = ref()

const status = reactive({
  loading: false
})

const form = reactive({
  name: '',
  company: '',
  service: '',
  countryCode: '+86',
  mobile: '',
  email: '',
  extra: ''
})

const serviceOptions = ref([
  { zh: '全球人才招聘（Recruitment）', en: 'Recruitment', ja: 'グローバル人材採用（Recruitment）' },
  { zh: '全球名义雇主（EOR）', en: 'EOR', ja: 'グローバル名義雇用主（EOR）' },
  { zh: '全球灵活员工（Contractor）', en: 'Contractor', ja: 'グローバルフレックスワーカー（Contractor）' },
  { zh: '全球人力资源服务（HRO）', en: 'HRO', ja: 'グローバル人材派遣（HRO）' },
  { zh: '其他', en: 'Other', ja: 'その他' }
])


async function submitForm() {
  // 防止重复提交
  if (status.loading) {
    return
  }

  await ruleFormRef.value.validate(async (valid, field) => {
    if (valid) {
      status.loading = true
      try {
        const res = await submitFormSage()
        const resFeishu = await submitFormFeishu()
        
        // 清空表单
        form.name = ''
        form.company = ''
        form.service = ''
        form.countryCode = '+86'
        form.mobile = ''
        form.email = ''
        form.extra = ''

        emit('submit')
      } catch (error) {
        console.error('表单提交失败:', error)
        ElMessage.error('提交失败，请重试')
      } finally {
        status.loading = false
      }
    }
  })
}

async function submitFormSage () {
  const host = "https://www-api.smartdeer.work/v1/website/function/runtime";
  const host_test = "https://www-api-test.smartdeer.work/v1/website/function/runtime";

  const res = await fetch(host, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'accept-language': 'en-US'
    },
    body: JSON.stringify({
      functionKey: "x_website_add_smd_website_leads",
      params: {
        "contactName": form.name,
        "companyName": form.company,
        "contactMobile": `${form.countryCode} ${form.mobile}`,
        "contactEmail": form.email,
        "serviceNeeded": form.service,
        "serviceContent": form.extra
      },
    }),
  })

  return res
}

async function submitFormFeishu () {
  // 使用新的referrer检测逻辑
  const { getCurrentReferrerInfo, formatReferrerForDisplay } = useReferrerDetection()
  const referrerInfo = getCurrentReferrerInfo()
  
  // console.log('📊 获取到的referrer信息:', referrerInfo);
  
  // 格式化用于显示
  const parsedRefRst = formatReferrerForDisplay(referrerInfo);
  
  // console.log('📤 准备发送到飞书的referrer信息:', parsedRefRst);

  const host = "https://open.feishu.cn/open-apis/bot/v2/hook/ab070be6-b904-4f95-8d2d-b03496f8202a"
  // const host_test = "https://open.feishu.cn/open-apis/bot/v2/hook/b47dd412-27fd-434b-8b79-6efe17951df8"

  const res = await fetch(host, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'accept-language': 'en-US'
    },
    body: JSON.stringify({
      msg_type: "post",
      content: {
        post: {
          zh_cn: {
            title: "Biu~, 您有新的网站咨询请求！",
            content: [
              [
                { tag: "text", text: "姓名: " },
                { tag: "text", text: form.name, }
              ], [
                { tag: "text", text: "公司: " },
                { tag: "text", text: form.company }
              ], [
                { tag: "text", text: "所需服务: " },
                { tag: "text", text: form.service }
              ], [
                { tag: "text", text: "邮箱: " },
                { tag: "text", text: form.email }
              ], [
                { tag: "text", text: "手机: " },
                { tag: "text", text: `${form.countryCode} ${form.mobile}` }
              ], [
                { tag: "text", text: "详细信息: " },
                { tag: "text", text: form.extra }
              ], [
                { tag: "text", text: "来源网站: " },
                { tag: "text", text: parsedRefRst.domain }
              ], [
                { tag: "text", text: "关键词: " },
                { tag: "text", text: parsedRefRst.keyword }
              ]
            ]
          }
        }
      }
    })
  })

  return res
}


</script>

<style lang="scss" scoped>
.contact-us-form {
  .form-title {
    font-size: 32px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30px;
    margin-top: -30px;
  }

  .form-body {
    padding: 0 80px;

    .mobile {
      line-height: 46px;
      border: 1px solid;
      border-radius: 8px;
      width: 100%;
    }
  }
}
</style>