/*
 * @Author: sx <EMAIL>
 * @Date: 2022-12-14 11:03:34
 * @LastEditors: sx <EMAIL>
 * @LastEditTime: 2023-01-12 15:50:34
 * @FilePath: \bpo-website-pc\assets\utils\lang.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const LANG_COOKIE_KEY = 'sd_intl_lang'

type Lang = 'en' | 'zh'

// 首页引导（增强版，支持更智能的语言检测）
export function indexGuide(referrerInfo?: any) {
  const config = useRuntimeConfig().public
  const result = window.matchMedia("(max-width: 700px)")
  const domain = result.matches ? config.mobile_site_host : config.pc_site_host

  // 使用增强的语言检测
  const { detectClientLanguage, setLanguagePreference } = useLanguageDetection()
  const detectedLang = detectClientLanguage()

  // 保存语言偏好
  setLanguagePreference(detectedLang)

  // 构建目标URL
  let targetUrl = `${domain ? domain : ''}/${detectedLang}`
  
  // 如果有referrer信息，将其作为URL参数传递
  if (referrerInfo && referrerInfo.domain) {
    const params = new URLSearchParams()
    params.set('ref', encodeURIComponent(referrerInfo.domain))
    if (referrerInfo.keyword) {
      params.set('kw', encodeURIComponent(referrerInfo.keyword))
    }
    if (referrerInfo.source) {
      params.set('src', encodeURIComponent(referrerInfo.source))
    }
    targetUrl += `?${params.toString()}`
  }

  console.log('🔄 跳转到目标URL:', targetUrl)
  location.replace(targetUrl)
}

// 切换语言（增强版，使用新的语言检测系统）
export function swithLang(lang: Lang, path: string = '') {
  const { setLanguagePreference, generateLanguageUrl } = useLanguageDetection()

  // 保存语言偏好
  setLanguagePreference(lang)

  // 生成目标URL
  const targetUrl = generateLanguageUrl(lang, path || window.location.pathname)

  location.replace(targetUrl)
}

// 获取当前页面的语言版本
export function getCurrentPageLanguage(): Lang {
  const { getCurrentLanguage } = useLanguageDetection()
  return getCurrentLanguage() as Lang
}

// 检查是否支持指定语言
export function isSupportedLanguage(lang: string): boolean {
  const { supportedLanguages } = useLanguageDetection()
  return lang in supportedLanguages
}

export default {
  indexGuide,
  swithLang
}