#!/usr/bin/env node

/**
 * SmartDeer网站SEO优化工具
 * 自动修复SEO问题，优化meta标签和页面标题
 */

const fs = require('fs');
const path = require('path');

class SEOOptimizer {
  constructor() {
    this.optimizations = [];
    this.seoTemplates = this.loadSEOTemplates();
  }

  // 加载SEO模板配置
  loadSEOTemplates() {
    return {
      zh: {
        homepage: {
          title: 'SmartDeer - 中国企业出海HR服务 | 全球雇佣&EOR解决方案',
          description: 'SmartDeer专注中国企业出海HR服务，提供EOR服务费用计算器、海外员工合规雇佣指南、跨境电商人才招聘。覆盖150+国家，助力企业安全合规出海。立即获取免费咨询！',
          keywords: '中国企业出海HR服务, EOR服务费用计算器, 海外员工合规雇佣指南, 跨境电商人才招聘, 一带一路国家雇佣服务, 远程工作全球合规管理, 科技公司海外扩张HR, 全球薪酬税务优化方案, SmartDeer'
        },
        aboutus: {
          title: '关于SmartDeer - 全球人力资源解决方案领导者 | ISO认证EOR服务商',
          description: 'SmartDeer成立于2020年，专注全球人力资源服务，拥有ISO 27001认证，在150+国家提供EOR、招聘、薪酬管理服务。全球设有办事处，为5000+企业提供专业出海HR解决方案。',
          keywords: '关于SmartDeer, 全球人力资源公司, EOR服务商, ISO认证, 国际雇佣专家, 出海企业服务, 全球办事处, 人力资源解决方案, 企业简介'
        },
        calculator: {
          title: 'EOR服务费用计算器 - 免费海外雇佣成本评估工具 | SmartDeer',
          description: '使用SmartDeer免费EOR服务费用计算器，精准计算150+国家海外雇佣成本、税费、社保。为中国企业出海提供透明化预算规划，助力明智的跨境用工决策。',
          keywords: 'EOR服务费用计算器, 海外雇佣成本评估, 中国企业出海成本, 跨境用工费用分析, 全球薪酬计算器, 国际招聘预算工具, 免费HR计算器'
        },
        countries: {
          title: '海外员工合规雇佣指南 - 150+国家劳动法规大全 | SmartDeer',
          description: '查看SmartDeer海外员工合规雇佣指南，获取150+国家最新劳动法规、一带一路国家政策、东南亚雇佣法规。专业HR合规信息，助力中国企业海外扩张决策。',
          keywords: '海外员工合规雇佣指南, 一带一路国家雇佣服务, 东南亚市场雇佣指南, 全球劳动法规, 海外税务政策, HR合规信息, 中国企业出海指南'
        },
        marketing: {
          title: '全球HR资讯 - 出海企业案例&国际雇佣趋势分析 | SmartDeer',
          description: '获取最新全球HR行业动态、出海企业成功案例、国际雇佣趋势分析。SmartDeer专业团队分享行业洞察，助力企业制定全球化人力资源策略，把握海外市场机遇。',
          keywords: '全球HR资讯, 出海企业案例, 国际雇佣趋势, HR行业动态, 海外扩张案例, 全球化策略, 人力资源趋势'
        },
        services: {
          title: '全球EOR/PEO服务 - 海外雇佣合规解决方案 | SmartDeer',
          description: 'SmartDeer提供专业EOR/PEO服务，涵盖全球雇佣、薪酬管理、税务合规、福利管理。150+国家本地化服务，确保海外用工100%合规，降低法律风险，加速企业全球化进程。',
          keywords: 'EOR服务, PEO服务, 全球雇佣, 海外用工合规, 国际薪酬管理, 税务合规, 福利管理, 雇主责任, 海外人事外包'
        }
      },
      en: {
        homepage: {
          title: 'SmartDeer - Hire in Singapore Without Entity | Digital Nomad Employment',
          description: 'SmartDeer enables you to hire in Singapore without entity setup. Specialized in digital nomad employment solutions, startup international expansion, and automated global payroll solutions. GDPR compliant international hiring across 150+ countries.',
          keywords: 'hire in Singapore without entity, digital nomad employment solutions, startup international expansion, automated global payroll solutions, GDPR compliant international hiring, employ staff in Hong Kong remotely, SmartDeer'
        },
        aboutus: {
          title: 'About SmartDeer - Global HR Solutions Leader | ISO Certified EOR Provider',
          description: 'Founded in 2020, SmartDeer specializes in global HR services with ISO 27001 certification. We provide EOR, recruitment, and payroll services across 150+ countries with global offices, serving 5000+ companies for overseas expansion.',
          keywords: 'about SmartDeer, global HR company, EOR provider, ISO certification, international employment expert, overseas business services, global offices, HR solutions, company profile'
        },
        calculator: {
          title: 'Automated Global Payroll Solutions - EOR ROI Calculator | SmartDeer',
          description: 'Use SmartDeer\'s automated global payroll solutions and EOR ROI calculator to reduce international hiring costs. Perfect for startup international expansion and digital nomad employment cost analysis across 150+ countries.',
          keywords: 'automated global payroll solutions, EOR ROI calculator, reduce international hiring costs, startup international expansion costs, digital nomad employment calculator, global employment cost savings, free HR tools'
        },
        countries: {
          title: 'Global Employment Guide - 150+ Countries Labor Laws & Tax Policies | SmartDeer',
          description: 'Access SmartDeer\'s comprehensive global employment guide with latest labor laws, tax policies, salary standards, and benefit requirements for 150+ countries. Professional HR compliance information for overseas expansion.',
          keywords: 'global employment guide, country labor laws, overseas tax policies, HR compliance information, international salary standards, employment laws, expansion guide, global employment policies'
        },
        services: {
          title: 'Global EOR/PEO Services - International Employment Compliance Solutions | SmartDeer',
          description: 'SmartDeer offers professional EOR/PEO services including global employment, payroll management, tax compliance, and benefits administration. 150+ countries localized services ensure 100% compliant overseas employment.',
          keywords: 'EOR services, PEO services, global employment, international employment compliance, global payroll management, tax compliance, benefits management, employer of record, international HR outsourcing'
        }
      },
      ja: {
        homepage: {
          title: 'SmartDeer - 海外雇用リスク管理 | 日系企業海外展開支援',
          description: 'SmartDeerは海外雇用リスク管理と日系企業海外展開支援に特化。シンガポール法人設立不要雇用、ISO認証EORサービス、製造業グローバル人事戦略を提供。150+ヶ国対応の高品質国際人事代行サービス。',
          keywords: '海外雇用リスク管理, 日系企業海外展開支援, シンガポール法人設立不要雇用, ISO認証EORサービス, 製造業グローバル人事戦略, タイ工場人事管理代行, ベトナム現地採用支援, SmartDeer'
        },
        aboutus: {
          title: '会社概要 - 海外雇用リスク管理専門家 | 日系企業海外展開支援リーダー',
          description: 'SmartDeerは海外雇用リスク管理の専門家として、日系企業の海外展開を支援。ISO認証EORサービス提供者として、信頼できる海外雇用パートナーとして150+ヶ国で実績豊富なグローバルHRサービスを提供。',
          keywords: 'SmartDeerについて, 海外雇用リスク管理専門家, 日系企業海外展開支援, ISO認証EORサービス, 信頼できる海外雇用パートナー, 実績豊富なグローバルHR, 会社概要'
        },
        calculator: {
          title: '海外給与計算代行サービス - 国際税務最適化コンサルティング | SmartDeer',
          description: 'SmartDeerの海外給与計算代行サービスと国際税務最適化コンサルティングで、グローバル雇用コストを分析。日系企業の海外進出人事費用を透明化し、効率的な国際人事戦略をサポート。',
          keywords: '海外給与計算代行サービス, 国際税務最適化コンサルティング, グローバル雇用コスト分析, 海外進出人事費用計算, 日系企業海外展開コスト, 無料HR計算ツール'
        },
        countries: {
          title: '国別雇用ガイド - シンガポール法人設立不要雇用 | タイ工場人事管理',
          description: 'SmartDeerの国別雇用ガイドで、シンガポール法人設立不要雇用、タイ工場人事管理代行、ベトナム現地採用支援など、ASEAN諸国の人事戦略を詳しく解説。海外雇用リスク管理の専門情報を提供。',
          keywords: 'シンガポール法人設立不要雇用, タイ工場人事管理代行, ベトナム現地採用支援, インド開発拠点人事管理, 中国工場労務管理代行, ASEAN諸国人事戦略'
        }
      }
    };
  }

  // 优化主要页面
  async optimizeMainPages() {
    console.log('🔧 开始优化主要页面...');

    // 修复根目录index.vue的noindex问题
    await this.fixRootIndexPage();

    // 优化中文主要页面
    await this.optimizePage('pages/zh/index.vue', 'zh', 'homepage');
    await this.optimizePage('pages/zh/aboutus.vue', 'zh', 'aboutus');
    await this.optimizePage('pages/zh/calculator.vue', 'zh', 'calculator');

    // 优化英文主要页面
    await this.optimizePage('pages/en/index.vue', 'en', 'homepage');
    await this.optimizePage('pages/en/aboutus.vue', 'en', 'aboutus');
    await this.optimizePage('pages/en/calculator.vue', 'en', 'calculator');

    // 优化日文主要页面
    await this.optimizePage('pages/ja/index.vue', 'ja', 'homepage');
    await this.optimizePage('pages/ja/aboutus.vue', 'ja', 'aboutus');
    await this.optimizePage('pages/ja/calculator.vue', 'ja', 'calculator');

    console.log('✅ 主要页面优化完成');
  }

  // 修复根目录index.vue的noindex问题
  async fixRootIndexPage() {
    const filePath = 'pages/index.vue';
    if (!fs.existsSync(filePath)) return;

    try {
      let content = fs.readFileSync(filePath, 'utf8');
      
      // 将noindex改为index
      content = content.replace(
        /robots['"`]:\s*['"`]noindex,\s*follow['"`]/,
        'robots\': \'index, follow\''
      );

      // 优化标题和描述
      const template = this.seoTemplates.zh.homepage;
      content = content.replace(
        /title:\s*['"`]([^'"`]*?)['"`]/,
        `title: '${template.title}'`
      );

      content = content.replace(
        /name:\s*['"`]description['"`]\s*,\s*content:\s*['"`]([^'"`]*?)['"`]/,
        `name: 'description', content: '${template.description}'`
      );

      fs.writeFileSync(filePath, content);
      this.optimizations.push(`✅ 修复根目录index.vue的索引设置`);
      console.log('✅ 修复根目录index.vue的索引设置');
    } catch (error) {
      console.error(`❌ 修复根目录index.vue失败:`, error.message);
    }
  }

  // 优化单个页面
  async optimizePage(filePath, language, pageType) {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  页面不存在: ${filePath}`);
      return;
    }

    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const template = this.seoTemplates[language]?.[pageType];
      
      if (!template) {
        console.log(`⚠️  没有找到模板: ${language}/${pageType}`);
        return;
      }

      let modified = false;

      // 优化标题
      if (template.title) {
        const titleRegex = /title:\s*['"`]([^'"`]*?)['"`]/;
        if (titleRegex.test(content)) {
          content = content.replace(titleRegex, `title: '${template.title}'`);
          modified = true;
        }
      }

      // 优化描述
      if (template.description) {
        const descRegex = /name:\s*['"`]description['"`]\s*,\s*content:\s*['"`]([^'"`]*?)['"`]/;
        if (descRegex.test(content)) {
          content = content.replace(descRegex, `name: 'description', content: '${template.description}'`);
          modified = true;
        }
      }

      // 优化关键词
      if (template.keywords) {
        const keywordsRegex = /name:\s*['"`]keywords['"`]\s*,\s*content:\s*['"`]([^'"`]*?)['"`]/;
        if (keywordsRegex.test(content)) {
          content = content.replace(keywordsRegex, `name: 'keywords', content: '${template.keywords}'`);
          modified = true;
        }
      }

      if (modified) {
        fs.writeFileSync(filePath, content);
        this.optimizations.push(`✅ 优化页面: ${filePath}`);
        console.log(`✅ 优化页面: ${filePath}`);
      }
    } catch (error) {
      console.error(`❌ 优化页面失败 ${filePath}:`, error.message);
    }
  }

  // 为缺少SEO配置的页面添加基础配置
  async addBasicSEOToPages() {
    console.log('🔧 为缺少SEO配置的页面添加基础配置...');
    
    // 读取审计报告
    const reportPath = 'SEO_AUDIT_REPORT.json';
    if (!fs.existsSync(reportPath)) {
      console.log('❌ 找不到审计报告，请先运行SEO审计');
      return;
    }

    const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
    const pagesNeedingSEO = report.pages.filter(page => 
      page.issues.includes('缺少描述') || page.issues.includes('缺少标题')
    );

    console.log(`📝 找到 ${pagesNeedingSEO.length} 个需要添加SEO配置的页面`);

    for (const page of pagesNeedingSEO.slice(0, 10)) { // 限制处理前10个页面
      await this.addBasicSEO(page);
    }

    console.log('✅ 基础SEO配置添加完成');
  }

  // 为单个页面添加基础SEO配置
  async addBasicSEO(pageData) {
    const filePath = `pages/${pageData.path}`;
    if (!fs.existsSync(filePath)) return;

    try {
      let content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否已有useHead配置
      if (content.includes('useHead(')) {
        console.log(`⚠️  页面已有SEO配置: ${pageData.path}`);
        return;
      }

      // 生成基础SEO配置
      const seoConfig = this.generateBasicSEO(pageData);
      
      // 在script标签后添加SEO配置
      const scriptMatch = content.match(/<script[^>]*setup[^>]*>/);
      if (scriptMatch) {
        const insertPosition = content.indexOf(scriptMatch[0]) + scriptMatch[0].length;
        const seoBlock = `\n\ndefinePageMeta({ layout: 'basic' })\n${seoConfig}\n`;
        
        content = content.slice(0, insertPosition) + seoBlock + content.slice(insertPosition);
        
        fs.writeFileSync(filePath, content);
        this.optimizations.push(`✅ 添加SEO配置: ${pageData.path}`);
        console.log(`✅ 添加SEO配置: ${pageData.path}`);
      }
    } catch (error) {
      console.error(`❌ 添加SEO配置失败 ${pageData.path}:`, error.message);
    }
  }

  // 生成基础SEO配置
  generateBasicSEO(pageData) {
    const lang = pageData.language;
    const langCode = lang === 'zh' ? 'zh-CN' : lang === 'ja' ? 'ja-JP' : 'en-US';
    
    // 根据页面路径生成标题和描述
    let title = this.generateTitleFromPath(pageData.path, lang);
    let description = this.generateDescriptionFromPath(pageData.path, lang);
    
    return `useHead({
  htmlAttrs: { lang: '${langCode}' },
  title: '${title}',
  meta: [
    { name: 'description', content: '${description}' },
    { name: 'keywords', content: '${this.generateKeywords(pageData.path, lang)}' },
    { name: 'author', content: 'SmartDeer' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },
    
    // Open Graph
    { property: 'og:title', content: '${title}' },
    { property: 'og:description', content: '${description}' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: '${pageData.url}' },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:locale', content: '${langCode.replace('-', '_')}' }
  ]
})`;
  }

  // 从路径生成标题
  generateTitleFromPath(path, lang) {
    const pathParts = path.replace('.vue', '').split('/');
    const pageName = pathParts[pathParts.length - 1];
    
    const titleMappings = {
      zh: {
        marketing: 'SmartDeer营销资讯 - 全球HR行业动态',
        countries: '全球国家雇佣指南 - SmartDeer',
        legal: '法律条款 - SmartDeer',
        articles: '文章资讯 - SmartDeer全球HR解决方案'
      },
      en: {
        marketing: 'SmartDeer Marketing News - Global HR Industry Updates',
        countries: 'Global Country Employment Guide - SmartDeer',
        legal: 'Legal Terms - SmartDeer',
        articles: 'Articles & Insights - SmartDeer Global HR Solutions'
      },
      ja: {
        marketing: 'SmartDeerマーケティングニュース - グローバルHR業界動向',
        countries: 'グローバル国別雇用ガイド - SmartDeer',
        legal: '法的条項 - SmartDeer',
        articles: '記事・インサイト - SmartDeerグローバルHRソリューション'
      }
    };

    return titleMappings[lang]?.[pageName] || `${pageName} - SmartDeer`;
  }

  // 从路径生成描述
  generateDescriptionFromPath(path, lang) {
    const descMappings = {
      zh: {
        marketing: 'SmartDeer营销资讯页面，提供最新的全球HR行业动态、出海企业案例分析、国际雇佣趋势洞察，助力企业制定更好的全球化人力资源策略。',
        countries: 'SmartDeer全球国家雇佣指南，提供150+国家的HR合规信息、劳动法规、税务政策，为您的海外扩张提供专业指导。',
        legal: 'SmartDeer法律条款页面，了解我们的服务条款、隐私政策、用户协议等重要法律信息。',
        articles: 'SmartDeer文章资讯页面，提供全球HR行业深度分析、出海企业实践案例、国际雇佣最佳实践等专业内容。'
      },
      en: {
        marketing: 'SmartDeer marketing news and updates, featuring the latest global HR industry trends, overseas expansion case studies, and international employment insights.',
        countries: 'SmartDeer global country employment guide, providing HR compliance information, labor regulations, and tax policies for 150+ countries.',
        legal: 'SmartDeer legal terms page, including our terms of service, privacy policy, user agreements and other important legal information.',
        articles: 'SmartDeer articles and insights, featuring in-depth global HR industry analysis, overseas business case studies, and international employment best practices.'
      }
    };

    const pathParts = path.replace('.vue', '').split('/');
    const pageName = pathParts[pathParts.length - 1];
    
    return descMappings[lang]?.[pageName] || `SmartDeer ${pageName} page providing professional global HR solutions and services.`;
  }

  // 生成关键词
  generateKeywords(path, lang) {
    const keywordMappings = {
      zh: {
        marketing: 'HR行业动态, 出海企业案例, 全球雇佣趋势, 国际HR资讯, 海外扩张案例',
        countries: '全球雇佣指南, 国家HR政策, 劳动法规, 海外合规, 国际税务',
        legal: '法律条款, 服务条款, 隐私政策, 用户协议, 法律声明',
        articles: 'HR文章, 全球雇佣资讯, 国际招聘, 出海指南, 人力资源'
      },
      en: {
        marketing: 'HR industry news, overseas expansion cases, global employment trends, international HR insights',
        countries: 'global employment guide, country HR policies, labor regulations, overseas compliance',
        legal: 'legal terms, terms of service, privacy policy, user agreement, legal notice',
        articles: 'HR articles, global employment news, international recruitment, overseas guide'
      }
    };

    const pathParts = path.replace('.vue', '').split('/');
    const pageName = pathParts[pathParts.length - 1];
    
    return keywordMappings[lang]?.[pageName] || 'SmartDeer, global HR, international employment';
  }

  // 运行所有优化
  async runOptimizations() {
    console.log('🚀 开始SEO优化...\n');
    
    await this.optimizeMainPages();
    await this.addBasicSEOToPages();
    
    console.log('\n📊 优化总结:');
    this.optimizations.forEach(opt => console.log(opt));
    console.log(`\n✅ 总共完成 ${this.optimizations.length} 项优化`);
  }
}

// 运行优化
if (require.main === module) {
  const optimizer = new SEOOptimizer();
  optimizer.runOptimizations().catch(console.error);
}

module.exports = SEOOptimizer;
