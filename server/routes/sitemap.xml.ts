// SmartDeer 动态 Sitemap 生成器 - PC版本
// 针对百度和必应优化的 XML Sitemap

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig()
  // PC项目使用PC域名
  const baseUrl = config.public.pc_site_host || 'https://www.smartdeer.com'
  
  // 定义支持的语言
  const languages = ['zh', 'en', 'ja']
  
  // 定义主要页面
  const mainPages = [
    '',
    'aboutus',
    'calculator',
    'countries',
    'marketing'
  ]
  
  // 定义文章页面（这里可以从数据库或API获取）
  const articlePages = [
    'articles/global-employment-guide',
    'articles/eor-vs-peo',
    'articles/global-payroll-management',
    'articles/remote-work-compliance'
  ]
  
  // 生成 URL 条目
  const urls: Array<{
    loc: string
    lastmod: string
    changefreq: string
    priority: string
    alternates?: Array<{ hreflang: string; href: string }>
  }> = []
  
  // 当前时间
  const now = new Date().toISOString().split('T')[0]
  
  // 生成主页面的多语言版本
  for (const page of mainPages) {
    for (const lang of languages) {
      const path = page ? `/${lang}/${page}` : `/${lang}/`
      const url = {
        loc: `${baseUrl}${path}`,
        lastmod: now,
        changefreq: page === '' ? 'daily' : 'weekly',
        priority: page === '' ? '1.0' : '0.8',
        alternates: languages.map(altLang => ({
          hreflang: altLang === 'zh' ? 'zh-CN' : altLang === 'ja' ? 'ja-JP' : 'en-US',
          href: `${baseUrl}/${altLang}/${page}`
        }))
      }
      urls.push(url)
    }
  }
  
  // 生成文章页面
  for (const article of articlePages) {
    for (const lang of languages) {
      const path = `/${lang}/${article}`
      const url = {
        loc: `${baseUrl}${path}`,
        lastmod: now,
        changefreq: 'monthly',
        priority: '0.6'
      }
      urls.push(url)
    }
  }
  
  // 生成 XML
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" 
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${urls.map(url => `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>${url.alternates ? url.alternates.map(alt => `
    <xhtml:link rel="alternate" hreflang="${alt.hreflang}" href="${alt.href}" />`).join('') : ''}
  </url>`).join('\n')}
</urlset>`

  // 设置响应头
  setHeader(event, 'Content-Type', 'application/xml')
  setHeader(event, 'Cache-Control', 'public, max-age=3600') // 缓存1小时
  
  return sitemap
})
